import os
from dotenv import load_dotenv
from telethon import TelegramClient, Button

load_dotenv()

API_ID = int(os.getenv("API_ID"))
API_HASH = os.getenv("API_HASH")
ADMIN_USER_ID = int(os.getenv("ADMIN_USER_ID"))
from telethon.tl.types import InputPeerUser
from telethon.sync import TelegramClient as SyncClient
from telethon.sessions import StringSession

from telethon import TelegramClient as UserClient
from telethon.tl.functions.users import GetFullUserRequest
from telethon.tl.functions.messages import SendMessageRequest
from telethon.tl.custom.button import Button as TelethonButton

import asyncio

bot_token = os.getenv("CONTROL_BOT_TOKEN")
bot = TelegramClient("bot_session", API_ID, API_HASH).start(bot_token=bot_token)

async def send_admin_buttons(user_id, username, full_name):
    buttons = [
        [
            Button.inline("Tag: Audit", data=f"tag_audit:{user_id}"),
            Button.inline("Tag: Pen Test", data=f"tag_pen_test:{user_id}")
        ],
        [
            Button.inline("Tag: Bug Bounty", data=f"tag_bug:{user_id}"),
            Button.inline("Tag: Monitoring", data=f"tag_monitoring:{user_id}")
        ],
        [
            Button.inline("Cold → Warm", data=f"status_warm:{user_id}"),
            Button.inline("Create Group", data=f"create_group:{user_id}")
        ],
        [
            Button.inline("Export Lead", data=f"export:{user_id}")
        ]
    ]

    await bot.send_message(
        ADMIN_USER_ID,
        f"Lead logged: {full_name} (@{username})",
        buttons=buttons
    )
