#!/bin/bash

# 🔐 Secure File Upload Script for Telegram Bot Deployment
# Ensures secure transfer of sensitive files to EC2 instance

set -euo pipefail

# Colors
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly NC='\033[0m'

# Usage function
usage() {
    echo "Usage: $0 <instance-ip> <ssh-key-path> <env-file>"
    echo ""
    echo "Example:"
    echo "  $0 ************ ~/.ssh/my-key.pem .env.john"
    echo ""
    echo "This script securely uploads your bot files to EC2 with proper permissions."
    exit 1
}

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

# Validate inputs
validate_inputs() {
    if [[ $# -ne 3 ]]; then
        usage
    fi
    
    local instance_ip="$1"
    local ssh_key="$2"
    local env_file="$3"
    
    # Validate IP format
    if ! [[ $instance_ip =~ ^[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}$ ]]; then
        error "Invalid IP address format: $instance_ip"
    fi
    
    # Check SSH key exists and has proper permissions
    if [[ ! -f "$ssh_key" ]]; then
        error "SSH key not found: $ssh_key"
    fi
    
    local key_perm=$(stat -c "%a" "$ssh_key" 2>/dev/null || stat -f "%A" "$ssh_key" 2>/dev/null)
    if [[ "$key_perm" != "600" ]]; then
        warn "SSH key permissions are $key_perm, should be 600"
        chmod 600 "$ssh_key"
        log "Fixed SSH key permissions"
    fi
    
    # Check environment file exists
    if [[ ! -f "$env_file" ]]; then
        error "Environment file not found: $env_file"
    fi
    
    log "✅ Input validation passed"
}

# Security check for environment file
check_env_security() {
    local env_file="$1"
    
    log "🔐 Checking environment file security..."
    
    # Check file permissions
    chmod 600 "$env_file"
    
    # Validate required variables
    local required_vars=("API_ID" "API_HASH" "ADMIN_USER_ID" "ADMIN_CHAT_ID" "CONTROL_BOT_TOKEN")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^${var}=" "$env_file"; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        error "Missing required variables in $env_file: ${missing_vars[*]}"
    fi
    
    # Check for common security issues
    if grep -q "password\|secret\|key" "$env_file" | grep -q "example\|test\|demo"; then
        warn "⚠️  Environment file contains example/test values"
    fi
    
    log "✅ Environment file security check passed"
}

# Test SSH connection
test_connection() {
    local instance_ip="$1"
    local ssh_key="$2"
    
    log "🔗 Testing SSH connection..."
    
    if ssh -i "$ssh_key" -o ConnectTimeout=10 -o StrictHostKeyChecking=no ubuntu@"$instance_ip" "echo 'Connection test successful'" >/dev/null 2>&1; then
        log "✅ SSH connection successful"
    else
        error "❌ Cannot connect to $instance_ip. Check IP, key, and security group."
    fi
}

# Create secure upload package
create_upload_package() {
    local env_file="$1"
    
    log "📦 Creating secure upload package..."
    
    # Create temporary directory
    local temp_dir=$(mktemp -d)
    local package_dir="$temp_dir/telegram-bot"
    mkdir -p "$package_dir"
    
    # Copy bot files (exclude sensitive and unnecessary files)
    local files_to_copy=(
        "lead_tracker.py"
        "group_manager.py"
        "google_sheets.py"
        "admin_commands.py"
        "requirements.txt"
        "secure-deploy.sh"
        "Logo.jpg"
        "storage.json"
    )
    
    for file in "${files_to_copy[@]}"; do
        if [[ -f "$file" ]]; then
            cp "$file" "$package_dir/"
            log "✓ Copied $file"
        else
            warn "⚠️  File not found: $file"
        fi
    done
    
    # Copy environment file as .env
    cp "$env_file" "$package_dir/.env"
    chmod 600 "$package_dir/.env"
    log "✓ Copied environment file"
    
    # Copy credentials.json if exists
    if [[ -f "credentials.json" ]]; then
        cp "credentials.json" "$package_dir/"
        chmod 600 "$package_dir/credentials.json"
        log "✓ Copied Google credentials"
    else
        warn "⚠️  credentials.json not found - Google Sheets may not work"
    fi
    
    # Set proper permissions
    chmod 755 "$package_dir"
    chmod 644 "$package_dir"/*.py 2>/dev/null || true
    chmod 755 "$package_dir"/*.sh 2>/dev/null || true
    
    echo "$package_dir"
}

# Secure upload to EC2
secure_upload() {
    local instance_ip="$1"
    local ssh_key="$2"
    local package_dir="$3"
    
    log "🚀 Uploading files securely to EC2..."
    
    # Upload with rsync for better handling
    if command -v rsync >/dev/null 2>&1; then
        rsync -avz --progress -e "ssh -i $ssh_key -o StrictHostKeyChecking=no" \
            "$package_dir/" ubuntu@"$instance_ip":/home/<USER>/telegram-bot/
    else
        # Fallback to scp
        scp -i "$ssh_key" -o StrictHostKeyChecking=no -r \
            "$package_dir/" ubuntu@"$instance_ip":/home/<USER>/telegram-bot/
    fi
    
    log "✅ Files uploaded successfully"
}

# Verify upload and set permissions
verify_upload() {
    local instance_ip="$1"
    local ssh_key="$2"
    
    log "🔍 Verifying upload and setting permissions..."
    
    # Connect and verify files
    ssh -i "$ssh_key" -o StrictHostKeyChecking=no ubuntu@"$instance_ip" << 'EOF'
        cd /home/<USER>/telegram-bot
        
        echo "📁 Files in upload directory:"
        ls -la
        
        echo ""
        echo "🔐 Setting secure permissions..."
        
        # Set proper permissions
        chmod 600 .env 2>/dev/null || echo "⚠️  .env not found"
        chmod 600 credentials.json 2>/dev/null || echo "⚠️  credentials.json not found"
        chmod 644 *.py 2>/dev/null || true
        chmod 755 *.sh 2>/dev/null || true
        
        echo "✅ Permissions set"
        
        echo ""
        echo "🔍 Security check:"
        echo "Environment file permissions: $(stat -c "%a" .env 2>/dev/null || echo "not found")"
        echo "Credentials file permissions: $(stat -c "%a" credentials.json 2>/dev/null || echo "not found")"
        
        echo ""
        echo "✅ Upload verification complete"
EOF
    
    log "✅ Upload verified and secured"
}

# Cleanup function
cleanup() {
    if [[ -n "${temp_dir:-}" ]] && [[ -d "$temp_dir" ]]; then
        rm -rf "$temp_dir"
        log "🧹 Cleaned up temporary files"
    fi
}

# Main function
main() {
    local instance_ip="$1"
    local ssh_key="$2"
    local env_file="$3"
    
    log "🚀 Starting secure upload process..."
    
    # Set cleanup trap
    trap cleanup EXIT
    
    validate_inputs "$@"
    check_env_security "$env_file"
    test_connection "$instance_ip" "$ssh_key"
    
    local package_dir=$(create_upload_package "$env_file")
    temp_dir=$(dirname "$package_dir")
    
    secure_upload "$instance_ip" "$ssh_key" "$package_dir"
    verify_upload "$instance_ip" "$ssh_key"
    
    log ""
    log "✅ Secure upload completed successfully!"
    log ""
    log "📋 Next steps:"
    log "1. SSH to your instance: ssh -i $ssh_key ubuntu@$instance_ip"
    log "2. Run deployment: cd /home/<USER>/telegram-bot && chmod +x secure-deploy.sh && ./secure-deploy.sh"
    log "3. Start the bot: sudo systemctl start telegram-bot"
    log ""
    log "🎉 Your files are now securely uploaded!"
}

# Check if script is being sourced or executed
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
