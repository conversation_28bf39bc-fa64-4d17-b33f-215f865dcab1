#!/bin/bash

# AWS Cost Monitoring Script for Free Tier
# Monitors AWS usage to ensure you stay within free tier limits

set -euo pipefail

# Colors
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

header() {
    echo -e "${BLUE}$1${NC}"
}

# Check if AWS CLI is configured
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        error "AWS CLI not found. Please install it first."
        exit 1
    fi
    
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS CLI not configured. Run 'aws configure' first."
        exit 1
    fi
}

# Get current month date range
get_current_month() {
    local start_date=$(date +%Y-%m-01)
    local end_date=$(date +%Y-%m-%d)
    echo "$start_date $end_date"
}

# Check EC2 usage
check_ec2_usage() {
    header "EC2 Usage (Free Tier: 750 hours/month)"
    
    local dates=($(get_current_month))
    local start_date="${dates[0]}"
    local end_date="${dates[1]}"
    
    # Get EC2 usage hours
    local ec2_usage=$(aws ce get-usage-and-cost \
        --time-period Start="$start_date",End="$end_date" \
        --granularity MONTHLY \
        --metrics UsageQuantity \
        --group-by Type=DIMENSION,Key=SERVICE \
        --filter file://<(cat << EOF
{
    "Dimensions": {
        "Key": "SERVICE",
        "Values": ["Amazon Elastic Compute Cloud - Compute"]
    }
}
EOF
) \
        --query 'ResultsByTime[0].Groups[0].Metrics.UsageQuantity.Amount' \
        --output text 2>/dev/null || echo "0")
    
    local hours=$(echo "$ec2_usage" | cut -d'.' -f1)
    local percentage=$((hours * 100 / 750))
    
    echo "Usage: $hours hours / 750 hours ($percentage%)"
    
    if [[ $hours -gt 750 ]]; then
        error "EXCEEDED FREE TIER LIMIT!"
    elif [[ $hours -gt 600 ]]; then
        warn "Approaching free tier limit (80%)"
    else
        log "Within free tier limits"
    fi
}

# Check EBS usage
check_ebs_usage() {
    header "EBS Storage (Free Tier: 30 GB/month)"
    
    # Get EBS volumes
    local volumes=$(aws ec2 describe-volumes \
        --query 'Volumes[?State==`in-use`].[VolumeId,Size,VolumeType]' \
        --output text)
    
    local total_size=0
    
    if [[ -n "$volumes" ]]; then
        while IFS=$'\t' read -r volume_id size volume_type; do
            echo "Volume $volume_id: ${size}GB ($volume_type)"
            total_size=$((total_size + size))
        done <<< "$volumes"
    fi
    
    local percentage=$((total_size * 100 / 30))
    
    echo "Total usage: ${total_size}GB / 30GB ($percentage%)"
    
    if [[ $total_size -gt 30 ]]; then
        error "  EXCEEDED FREE TIER LIMIT!"
    elif [[ $total_size -gt 24 ]]; then
        warn "  Approaching free tier limit (80%)"
    else
        log " Within free tier limits"
    fi
}

# Check data transfer
check_data_transfer() {
    header "🌐 Data Transfer (Free Tier: 15 GB outbound/month)"
    
    local dates=($(get_current_month))
    local start_date="${dates[0]}"
    local end_date="${dates[1]}"
    
    # This is a simplified check - actual data transfer monitoring requires more complex queries
    log " Data transfer monitoring requires detailed CloudWatch metrics"
    log " For a Telegram bot, data usage is typically very low"
    log " Monitor your AWS billing dashboard for exact usage"
}

# Check CloudWatch logs
check_cloudwatch_logs() {
    header " CloudWatch Logs (Free Tier: 5 GB storage)"
    
    # Get log groups
    local log_groups=$(aws logs describe-log-groups \
        --query 'logGroups[?contains(logGroupName, `telegram-bot`)].[logGroupName,storedBytes]' \
        --output text 2>/dev/null || echo "")
    
    local total_bytes=0
    
    if [[ -n "$log_groups" ]]; then
        while IFS=$'\t' read -r log_group bytes; do
            local mb=$((bytes / 1024 / 1024))
            echo "Log group $log_group: ${mb}MB"
            total_bytes=$((total_bytes + bytes))
        done <<< "$log_groups"
    fi
    
    local total_gb=$((total_bytes / 1024 / 1024 / 1024))
    local percentage=$((total_gb * 100 / 5))
    
    echo "Total usage: ${total_gb}GB / 5GB ($percentage%)"
    
    if [[ $total_gb -gt 5 ]]; then
        error "  EXCEEDED FREE TIER LIMIT!"
    elif [[ $total_gb -gt 4 ]]; then
        warn "  Approaching free tier limit (80%)"
    else
        log " Within free tier limits"
    fi
}

# Get current costs
check_current_costs() {
    header " Current Month Costs"
    
    local dates=($(get_current_month))
    local start_date="${dates[0]}"
    local end_date="${dates[1]}"
    
    # Get total costs
    local total_cost=$(aws ce get-cost-and-usage \
        --time-period Start="$start_date",End="$end_date" \
        --granularity MONTHLY \
        --metrics BlendedCost \
        --query 'ResultsByTime[0].Total.BlendedCost.Amount' \
        --output text 2>/dev/null || echo "0")
    
    echo "Total cost this month: \$${total_cost}"
    
    # Get costs by service
    local service_costs=$(aws ce get-cost-and-usage \
        --time-period Start="$start_date",End="$end_date" \
        --granularity MONTHLY \
        --metrics BlendedCost \
        --group-by Type=DIMENSION,Key=SERVICE \
        --query 'ResultsByTime[0].Groups[].[Keys[0],Metrics.BlendedCost.Amount]' \
        --output text 2>/dev/null || echo "")
    
    if [[ -n "$service_costs" ]]; then
        echo ""
        echo "Costs by service:"
        while IFS=$'\t' read -r service cost; do
            if (( $(echo "$cost > 0.01" | bc -l) )); then
                echo "  $service: \$${cost}"
            fi
        done <<< "$service_costs"
    fi
    
    # Cost alerts
    if (( $(echo "$total_cost > 1.00" | bc -l) )); then
        warn "  Monthly cost exceeds \$1.00"
    elif (( $(echo "$total_cost > 0.50" | bc -l) )); then
        warn "  Monthly cost exceeds \$0.50"
    else
        log " Costs are minimal"
    fi
}

# Check free tier eligibility
check_free_tier_eligibility() {
    header "🆓 Free Tier Eligibility"
    
    # Get account creation date (approximate)
    local account_id=$(aws sts get-caller-identity --query Account --output text)
    
    log "Account ID: $account_id"
    log " Free tier is available for 12 months from account creation"
    log " Check AWS Billing Dashboard for exact free tier status"
    log " https://console.aws.amazon.com/billing/home#/freetier"
}

# Generate recommendations
generate_recommendations() {
    header " Cost Optimization Recommendations"
    
    echo "1.  Stop instances when not needed:"
    echo "   aws ec2 stop-instances --instance-ids i-xxxxxxxxx"
    echo ""
    
    echo "2.  Set up billing alerts:"
    echo "   - Go to AWS Billing Dashboard"
    echo "   - Set up alerts for \$1, \$5, \$10"
    echo ""
    
    echo "3.  Clean up old resources:"
    echo "   - Delete unused EBS snapshots"
    echo "   - Remove old CloudWatch logs"
    echo "   - Terminate unused instances"
    echo ""
    
    echo "4.  Monitor regularly:"
    echo "   - Run this script weekly"
    echo "   - Check AWS Cost Explorer monthly"
    echo "   - Review free tier usage dashboard"
    echo ""
}

# Main function
main() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE} AWS Free Tier Cost Monitor${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
    
    check_aws_cli
    
    check_ec2_usage
    echo ""
    
    check_ebs_usage
    echo ""
    
    check_data_transfer
    echo ""
    
    check_cloudwatch_logs
    echo ""
    
    check_current_costs
    echo ""
    
    check_free_tier_eligibility
    echo ""
    
    generate_recommendations
    
    echo ""
    log " Cost monitoring complete"
    log " For detailed usage, visit AWS Billing Dashboard"
    log " https://console.aws.amazon.com/billing/home"
}

# Run if executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
