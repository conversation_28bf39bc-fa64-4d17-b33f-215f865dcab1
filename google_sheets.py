import gspread
from oauth2client.service_account import ServiceAccountCredentials
from googleapiclient.errors import HttpError
import os
import time
import logging
from secrets_loader import get_secret

# Configure structured logging
from logging_config import log_user_action
logger = logging.getLogger(__name__)

try:
    SHEET_NAME = get_secret("GOOGLE_SHEET_NAME")
except Exception:
    # Fallback for backward compatibility
    from dotenv import load_dotenv
    load_dotenv()
    SHEET_NAME = os.getenv("GOOGLE_SHEET_NAME")

SCOPES = ["https://www.googleapis.com/auth/spreadsheets", "https://www.googleapis.com/auth/drive"]

def get_sheets_client():
    """Get authenticated Google Sheets client with error handling"""
    try:
        creds = ServiceAccountCredentials.from_json_keyfile_name("credentials.json", SCOPES)
        client = gspread.authorize(creds)
        return client
    except Exception as e:
        logger.error(f"Failed to initialize Google Sheets client: {e}")
        raise

def sheets_api_call_with_retry(func, *args, max_retries=3, **kwargs):
    """Wrapper for Google Sheets API calls with exponential backoff on quota limits"""
    for attempt in range(max_retries):
        try:
            return func(*args, **kwargs)
        except HttpError as e:
            error_code = e.resp.status
            error_reason = e.error_details[0].get('reason', '') if e.error_details else ''

            # Handle quota exceeded errors
            if error_code == 429 or 'quota' in error_reason.lower() or 'rate' in error_reason.lower():
                wait_time = (2 ** attempt) * 60  # Exponential backoff in minutes for quota
                logger.warning(
                    f"Google Sheets quota limit hit, waiting {wait_time}s (attempt {attempt + 1}/{max_retries})",
                    extra={
                        'action': 'sheets_quota_limit',
                        'wait_time': wait_time,
                        'attempt': attempt + 1,
                        'error_code': error_code,
                        'error_reason': error_reason
                    }
                )
                if attempt < max_retries - 1:
                    time.sleep(wait_time)
                else:
                    logger.error(f"Max retries exceeded for Google Sheets API call")
                    raise
            else:
                # For other HTTP errors, don't retry
                logger.error(
                    f"Google Sheets API error: {e}",
                    extra={
                        'action': 'sheets_api_error',
                        'error_code': error_code,
                        'error_reason': error_reason
                    }
                )
                raise
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff for general errors
                logger.warning(
                    f"Google Sheets API call failed, retrying in {wait_time}s: {e}",
                    extra={
                        'action': 'sheets_api_retry',
                        'wait_time': wait_time,
                        'attempt': attempt + 1,
                        'error': str(e)
                    }
                )
                time.sleep(wait_time)
            else:
                logger.error(f"Google Sheets API call failed after {max_retries} attempts: {e}")
                raise

# Initialize client and sheet
try:
    client = get_sheets_client()
    sheet = client.open(SHEET_NAME).sheet1
    logger.info(f"Google Sheets initialized successfully: {SHEET_NAME}")
except Exception as e:
    logger.error(f"Failed to initialize Google Sheets: {e}")
    sheet = None

def get_existing_user_ids():
    """Get existing user IDs from the sheet with retry logic"""
    if not sheet:
        logger.error("Sheet not initialized")
        return set()

    try:
        records = sheets_api_call_with_retry(sheet.get_all_records)
        user_ids = {str(row["User ID"]) for row in records}
        logger.debug(f"Retrieved {len(user_ids)} existing user IDs")
        return user_ids
    except Exception as e:
        logger.error(
            f"Failed to get existing user IDs: {e}",
            extra={'action': 'get_existing_user_ids_failed', 'error': str(e)}
        )
        return set()

def log_lead(user_id, full_name, username, timestamp, bio="", message="", company=None, tags=None):
    """Log a new lead to Google Sheets with retry logic"""
    if not sheet:
        logger.error("Sheet not initialized, cannot log lead")
        return

    user_id = str(user_id)
    # Allow duplicates - each interaction is logged separately
    logger.info(f"Logging lead for user {user_id} (duplicates allowed)")

    row = [
        user_id,
        username,
        full_name,
        bio,
        message,
        timestamp,
        timestamp,  # last_contact = initial timestamp
        ", ".join(tags or []),
        company or "",
        "FALSE",  # Group Created
        "TRUE",   # Handled
    ]

    try:
        sheets_api_call_with_retry(sheet.append_row, row)
        logger.info(
            f"Lead logged successfully: {full_name} ({username})",
            extra={
                'action': 'lead_logged',
                'user_id': user_id,
                'username': username,
                'full_name': full_name,
                'company': company,
                'tags': tags
            }
        )
    except Exception as e:
        logger.error(
            f"Failed to log lead: {e}",
            extra={
                'action': 'lead_log_failed',
                'user_id': user_id,
                'username': username,
                'error': str(e)
            }
        )

def update_last_contact(user_id, new_timestamp):
    """Update last contact timestamp with retry logic"""
    if not sheet:
        logger.error("Sheet not initialized, cannot update last contact")
        return

    try:
        user_id = str(user_id)
        all_rows = sheets_api_call_with_retry(sheet.get_all_values)
        headers = all_rows[0]
        for i, row in enumerate(all_rows[1:], start=2):
            if row[0] == user_id:
                last_contact_col = headers.index("Last Contact") + 1
                sheets_api_call_with_retry(sheet.update_cell, i, last_contact_col, new_timestamp)
                logger.info(
                    f"Last contact updated for user {user_id}",
                    extra={
                        'action': 'last_contact_updated',
                        'user_id': user_id,
                        'timestamp': new_timestamp
                    }
                )
                break
    except Exception as e:
        logger.error(
            f"Error updating last_contact: {e}",
            extra={
                'action': 'last_contact_update_failed',
                'user_id': user_id,
                'error': str(e)
            }
        )

def update_lead_status(user_id, status):
    """Update lead status with retry logic"""
    if not sheet:
        logger.error("Sheet not initialized, cannot update lead status")
        return

    try:
        user_id = str(user_id)
        records = sheets_api_call_with_retry(sheet.get_all_records)
        for i, row in enumerate(records, start=2):
            if str(row.get("User ID")) == user_id:
                sheets_api_call_with_retry(sheet.update_cell, i, 5, status)  # Assuming Column E is 'Status'
                logger.info(
                    f"Lead status updated for user {user_id}: {status}",
                    extra={
                        'action': 'lead_status_updated',
                        'user_id': user_id,
                        'status': status
                    }
                )
                return
    except Exception as e:
        logger.error(
            f"Error updating lead status: {e}",
            extra={
                'action': 'lead_status_update_failed',
                'user_id': user_id,
                'status': status,
                'error': str(e)
            }
        )

def append_tag_to_lead(user_id, new_tag):
    """Append tag to lead with retry logic"""
    if not sheet:
        logger.error("Sheet not initialized, cannot append tag")
        return

    try:
        user_id = str(user_id)
        all_rows = sheets_api_call_with_retry(sheet.get_all_values)
        headers = all_rows[0]
        tag_index = headers.index("Tags")
        for i, row in enumerate(all_rows[1:], start=2):
            if row[0] == user_id:
                current_tags = row[tag_index].split(", ") if row[tag_index] else []
                if new_tag not in current_tags:
                    current_tags.append(new_tag)
                    updated_tags = ", ".join(current_tags)
                    sheets_api_call_with_retry(sheet.update_cell, i, tag_index + 1, updated_tags)
                    logger.info(
                        f"Tag appended for user {user_id}: {new_tag}",
                        extra={
                            'action': 'tag_appended',
                            'user_id': user_id,
                            'new_tag': new_tag,
                            'all_tags': updated_tags
                        }
                    )
                break
    except Exception as e:
        logger.error(
            f"Error appending tag: {e}",
            extra={
                'action': 'tag_append_failed',
                'user_id': user_id,
                'new_tag': new_tag,
                'error': str(e)
            }
        )

def get_lead_data(user_id):
    """Get lead data from Google Sheets for a specific user_id with retry logic"""
    if not sheet:
        logger.error("Sheet not initialized, cannot get lead data")
        return None

    try:
        user_id = str(user_id)
        all_rows = sheets_api_call_with_retry(sheet.get_all_values)
        headers = all_rows[0]

        for row in all_rows[1:]:
            if row[0] == user_id:
                # Map row data to dictionary using headers
                lead_data = {}
                for i, header in enumerate(headers):
                    lead_data[header] = row[i] if i < len(row) else ""

                result = {
                    'full_name': lead_data.get('Full Name', ''),
                    'username': lead_data.get('Username', ''),
                    'company': lead_data.get('Company', ''),
                    'tags': lead_data.get('Tags', ''),
                    'bio': lead_data.get('Bio', ''),
                    'message': lead_data.get('Message', '')
                }

                logger.debug(
                    f"Lead data retrieved for user {user_id}",
                    extra={
                        'action': 'lead_data_retrieved',
                        'user_id': user_id,
                        'full_name': result['full_name']
                    }
                )
                return result

        logger.warning(
            f"No lead data found for user {user_id}",
            extra={
                'action': 'lead_data_not_found',
                'user_id': user_id
            }
        )
        return None
    except Exception as e:
        logger.error(
            f"Error getting lead data: {e}",
            extra={
                'action': 'lead_data_get_failed',
                'user_id': user_id,
                'error': str(e)
            }
        )
        return None
