#!/usr/bin/env python3
"""
Secure Secrets Management for Telegram Bot
Handles loading secrets from AWS SSM Parameter Store with fallback to .env
Includes credential age monitoring and rotation warnings
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from dotenv import load_dotenv

logger = logging.getLogger(__name__)

class SecretsManager:
    """Manages secrets from AWS SSM Parameter Store with .env fallback"""
    
    def __init__(self, parameter_prefix: str = "/telegram-bot/"):
        self.parameter_prefix = parameter_prefix
        self.ssm_client = None
        self.use_ssm = False
        self.secrets_cache = {}
        self.credential_age_warning_days = 90
        
        # Try to initialize SSM client
        self._init_ssm_client()
        
        # Load .env as fallback
        load_dotenv()
    
    def _init_ssm_client(self):
        """Initialize SSM client if AWS credentials are available"""
        try:
            self.ssm_client = boto3.client('ssm')
            # Test connection
            self.ssm_client.describe_parameters(MaxResults=1)
            self.use_ssm = True
            logger.info("AWS SSM Parameter Store initialized successfully")
        except (NoCredentialsError, ClientError) as e:
            logger.warning(f"AWS SSM not available, falling back to .env: {e}")
            self.use_ssm = False
    
    def get_parameter(self, parameter_name: str, decrypt: bool = True) -> Optional[str]:
        """Get parameter from SSM Parameter Store"""
        try:
            full_name = f"{self.parameter_prefix}{parameter_name}"
            
            # Check cache first
            if full_name in self.secrets_cache:
                return self.secrets_cache[full_name]
            
            response = self.ssm_client.get_parameter(
                Name=full_name,
                WithDecryption=decrypt
            )
            
            value = response['Parameter']['Value']
            self.secrets_cache[full_name] = value
            
            # Check parameter age
            self._check_parameter_age(response['Parameter'])
            
            return value
            
        except ClientError as e:
            if e.response['Error']['Code'] == 'ParameterNotFound':
                logger.warning(f"Parameter not found in SSM: {parameter_name}")
            else:
                logger.error(f"Error retrieving parameter {parameter_name}: {e}")
            return None
    
    def _check_parameter_age(self, parameter: Dict[str, Any]):
        """Check if parameter is older than warning threshold"""
        try:
            last_modified = parameter.get('LastModifiedDate')
            if last_modified:
                age = datetime.now(last_modified.tzinfo) - last_modified
                if age.days > self.credential_age_warning_days:
                    logger.warning(
                        f"Parameter {parameter['Name']} is {age.days} days old. "
                        f"Consider rotating (threshold: {self.credential_age_warning_days} days)"
                    )
        except Exception as e:
            logger.debug(f"Could not check parameter age: {e}")
    
    def get_secret(self, key: str, required: bool = True) -> Optional[str]:
        """Get secret from SSM or .env with fallback logic"""
        value = None
        
        # Try SSM first if available
        if self.use_ssm:
            value = self.get_parameter(key)
        
        # Fallback to environment variable
        if value is None:
            value = os.getenv(key)
        
        # Handle required secrets
        if required and value is None:
            logger.error(f"Required secret '{key}' not found in SSM or environment")
            raise ValueError(f"Missing required secret: {key}")
        
        if value is None:
            logger.warning(f"Optional secret '{key}' not found")
        
        return value
    
    def get_all_secrets(self) -> Dict[str, str]:
        """Get all required secrets for the bot"""
        secrets = {}
        
        required_secrets = [
            'API_ID',
            'API_HASH', 
            'CONTROL_BOT_TOKEN',
            'ADMIN_USER_ID',
            'ADMIN_CHAT_ID',
            'GOOGLE_SHEET_NAME'
        ]
        
        optional_secrets = [
            'SESSION_ENCRYPTION_PASSWORD',
            'AWS_REGION',
            'BACKUP_S3_BUCKET',
            'CLOUDWATCH_LOG_GROUP'
        ]
        
        # Load required secrets
        for key in required_secrets:
            secrets[key] = self.get_secret(key, required=True)
        
        # Load optional secrets
        for key in optional_secrets:
            value = self.get_secret(key, required=False)
            if value:
                secrets[key] = value
        
        return secrets
    
    def store_parameter(self, parameter_name: str, value: str, 
                       description: str = "", secure: bool = True) -> bool:
        """Store parameter in SSM Parameter Store"""
        if not self.use_ssm:
            logger.error("SSM not available for storing parameters")
            return False
        
        try:
            full_name = f"{self.parameter_prefix}{parameter_name}"
            
            self.ssm_client.put_parameter(
                Name=full_name,
                Value=value,
                Description=description,
                Type='SecureString' if secure else 'String',
                Overwrite=True
            )
            
            logger.info(f"Parameter stored successfully: {parameter_name}")
            
            # Clear cache
            if full_name in self.secrets_cache:
                del self.secrets_cache[full_name]
            
            return True
            
        except ClientError as e:
            logger.error(f"Error storing parameter {parameter_name}: {e}")
            return False
    
    def migrate_from_env(self, env_file: str = ".env") -> bool:
        """Migrate secrets from .env file to SSM Parameter Store"""
        if not self.use_ssm:
            logger.error("SSM not available for migration")
            return False
        
        if not os.path.exists(env_file):
            logger.error(f"Environment file not found: {env_file}")
            return False
        
        try:
            # Load environment file
            with open(env_file, 'r') as f:
                lines = f.readlines()
            
            migrated_count = 0
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip().strip('"\'')
                    
                    if value and not value.startswith('YOUR_'):
                        success = self.store_parameter(
                            key, 
                            value, 
                            f"Migrated from {env_file}",
                            secure=True
                        )
                        if success:
                            migrated_count += 1
            
            logger.info(f"Successfully migrated {migrated_count} parameters to SSM")
            return True
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on secrets management"""
        health = {
            'ssm_available': self.use_ssm,
            'env_file_exists': os.path.exists('.env'),
            'secrets_loaded': len(self.secrets_cache),
            'warnings': []
        }
        
        if not self.use_ssm and not health['env_file_exists']:
            health['warnings'].append("Neither SSM nor .env file available")
        
        return health

# Global instance
secrets_manager = SecretsManager()

def get_secret(key: str, required: bool = True) -> Optional[str]:
    """Convenience function to get a secret"""
    return secrets_manager.get_secret(key, required)

def get_all_secrets() -> Dict[str, str]:
    """Convenience function to get all secrets"""
    return secrets_manager.get_all_secrets()

def migrate_secrets() -> bool:
    """Convenience function to migrate secrets to SSM"""
    return secrets_manager.migrate_from_env()

if __name__ == "__main__":
    # CLI interface for secrets management
    import argparse
    
    parser = argparse.ArgumentParser(description='Manage bot secrets')
    parser.add_argument('action', choices=['migrate', 'test', 'health'], 
                       help='Action to perform')
    
    args = parser.parse_args()
    
    if args.action == 'migrate':
        success = migrate_secrets()
        sys.exit(0 if success else 1)
    elif args.action == 'test':
        try:
            secrets = get_all_secrets()
            print(f"Successfully loaded {len(secrets)} secrets")
            sys.exit(0)
        except Exception as e:
            print(f"Failed to load secrets: {e}")
            sys.exit(1)
    elif args.action == 'health':
        health = secrets_manager.health_check()
        print(f"Health check: {health}")
        sys.exit(0)
