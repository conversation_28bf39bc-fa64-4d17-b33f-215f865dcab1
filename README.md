# Telegram Lead Tracker Bot - Complete Deployment Guide

## Overview

A production-ready, secure Telegram bot that automatically detects lead messages, logs them to Google Sheets, and provides admin controls through Telegram buttons. Optimized for AWS Free Tier with enterprise-grade security.

### What This Bot Does
- **Detects trigger messages**: Monitors outgoing messages for "hello from hacken"
- **Logs leads automatically**: Saves lead information to Google Sheets
- **Creates admin controls**: Provides buttons for tagging, status updates, and group creation
- **Auto-creates groups**: Makes new Telegram groups with company logos
- **Tracks interactions**: Prevents duplicate processing and maintains lead history

### Key Features
- $0 AWS costs (12-month free tier eligible)
- **Production-grade security** (encrypted sessions, rate limiting, audit trails)
- **Encrypted session handling** with AES-256-GCM encryption at rest
- **Rate limiting & backoff** for Telegram and Google Sheets APIs
- **Automated backups** to S3 with encryption and rotation
- **AWS SSM Parameter Store** integration for secure secrets management
- **Structured logging** with audit trails and log rotation
- Auto-restart on failures and boot-time startup
- Team collaboration ready with isolated deployments
- Easy deployment with automated scripts

---

## File Structure & Requirements

### Core Bot Files (12 files - minimum for local testing)
```
lead_tracker.py          # Main bot logic - detects triggers, processes leads
group_manager.py         # Group creation with company names and logos
google_sheets.py         # Sheets integration - logs leads, manages tags
admin_commands.py        # Admin functions - handles button clicks, status updates
secrets_loader.py        # Secure secrets management (SSM/env fallback)
logging_config.py        # Centralized logging with audit trails
requirements.txt         # Python dependencies
storage.json            # Bot state storage - prevents duplicate processing
Logo.jpg                # Hacken logo for group photos
credentials.json        # Google service account for Sheets access
.env                    # Your secrets (create from template)
.gitignore              # Git ignore file (excludes sensitive files)
```

### Security & Operations Files (8 files)
```
encrypt_session.py           # Session encryption/decryption tool
backup.sh                    # Automated backup script with S3 support
restore.sh                   # Backup restoration tool
cron-backup.conf             # Cron job configuration for backups
logrotate.conf               # Log rotation configuration
.env.template                # Environment template with all options
```

### AWS Deployment Files (6 files)
```
secure-deploy.sh              # AWS deployment script with security hardening
secure-upload.sh              # Secure file transfer to EC2
cloudformation-template.yaml  # AWS infrastructure (EC2, security groups, etc.)
team-setup.sh                 # Interactive credential helper
cost-monitor.sh               # AWS cost tracking for free tier
README.md                     # This documentation
```

### Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Telegram API  │◄──►│   EC2 Instance   │◄──►│  Google Sheets  │
│                 │    │                  │    │                 │
│ • Bot Messages  │    │ • Lead Tracker   │    │ • Lead Storage  │
│ • Admin Buttons │    │ • Auto-restart   │    │ • Tag Management│
│ • Group Creation│    │ • Monitoring     │    │ • Export Data   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │   CloudWatch     │
                       │                  │
                       │ • Logs (7 days)  │
                       │ • Metrics        │
                       │ • Alerts         │
                       └──────────────────┘
```

---

## Prerequisites & Setup

### Required Accounts & Tools
- [ ] AWS account (free tier eligible)
- [ ] Telegram account
- [ ] Google account (for Sheets integration)
- [ ] AWS CLI installed locally
- [ ] SSH client (Terminal/PuTTY)

### Required Information to Gather
- [ ] **Telegram API credentials** (from https://my.telegram.org)
- [ ] **Bot token** (from @BotFather)
- [ ] **Your Telegram user ID** (from @userinfobot)
- [ ] **Admin chat ID** (group where you want admin buttons)
- [ ] **Google Sheets name** (for lead tracking)
- [ ] **Google Service Account** credentials

---

## Step-by-Step Deployment

### Step 1: Get Telegram Credentials

#### Telegram API Credentials
1. Go to https://my.telegram.org
2. Log in with your phone number
3. Go to "API Development Tools"
4. Create new application
5. Note down **API ID** and **API Hash**

#### Bot Token
1. Message @BotFather on Telegram
2. Create new bot: `/newbot`
3. Follow instructions and choose a name
4. Note down **Bot Token**

#### Your User ID
1. Message @userinfobot on Telegram
2. Note down your **User ID**

#### Admin Chat ID
1. Create a group in Telegram for admin notifications
2. Add your bot to the group
3. Send a test message in the group
4. Visit: `https://api.telegram.org/botYOUR_BOT_TOKEN/getUpdates`
5. Find the chat ID (negative number for groups)

### Step 2: Setup Google Sheets Integration

#### Google Service Account
1. Go to Google Cloud Console (console.cloud.google.com)
2. Create new project or select existing one
3. Enable Google Sheets API
4. Create Service Account
5. Download credentials.json file
6. Create a Google Sheet for lead tracking
7. Share your Google Sheet with the service account email

### Step 3: Prepare Environment File

#### Option A: Use Interactive Setup (Recommended)
```bash
# Run the team setup script
chmod +x team-setup.sh
./team-setup.sh
```

#### Option B: Manual Setup
```bash
# Create environment file
cp .env.template .env.YOURNAME

# Edit with your credentials
nano .env.YOURNAME
```

### Step 4: Deploy to AWS

#### Deploy Infrastructure
```bash
# Deploy your personal instance
aws cloudformation create-stack \
  --stack-name telegram-bot-YOURNAME \
  --template-body file://cloudformation-template.yaml \
  --parameters ParameterKey=KeyPairName,ParameterValue=YOUR_KEY \
               ParameterKey=AllowedSSHIP,ParameterValue=YOUR_IP/32 \
               ParameterKey=TeamMemberName,ParameterValue=YOURNAME \
  --capabilities CAPABILITY_IAM

# Wait for completion
aws cloudformation wait stack-create-complete \
  --stack-name telegram-bot-YOURNAME

# Get instance IP
aws cloudformation describe-stacks \
  --stack-name telegram-bot-YOURNAME \
  --query 'Stacks[0].Outputs[?OutputKey==`InstanceIP`].OutputValue' \
  --output text
```

#### Upload Files and Deploy
```bash
# Upload files securely
./secure-upload.sh INSTANCE_IP your-key.pem .env.YOURNAME

# SSH to instance and deploy
ssh -i your-key.pem ubuntu@INSTANCE_IP
cd /home/<USER>/telegram-bot
chmod +x secure-deploy.sh
./secure-deploy.sh

# Start the bot
sudo systemctl start telegram-bot
sudo systemctl enable telegram-bot

# Set up session encryption (recommended for production)
./encrypt_session.py encrypt session.session
./encrypt_session.py encrypt bot_session.session

# Set up automated backups
sudo cp cron-backup.conf /etc/cron.d/telegram-bot-backup
sudo cp logrotate.conf /etc/logrotate.d/telegram-bot

# Migrate secrets to SSM (optional, for enhanced security)
python3 secrets_loader.py migrate
```

### Post-Deployment Security Setup

```bash
# Verify security configuration
./backup.sh --test
python3 secrets_loader.py health

# Test session encryption
ls -la *.session.enc  # Should see encrypted session files

# Check logging setup
sudo tail -f /var/log/telegram-bot/telegram-bot.log

# Verify firewall status
sudo ufw status

# Check fail2ban status
sudo fail2ban-client status
```

---

## Security Features

### Encrypted Sessions
- **Session file encryption**: Telegram sessions encrypted at rest using AES-256-GCM
- **Runtime decryption**: Sessions decrypted only in memory during bot operation
- **Automatic cleanup**: Decrypted sessions removed on shutdown
- **Password protection**: Encryption keys stored in AWS SSM or environment variables

```bash
# Encrypt existing session files
./encrypt_session.py encrypt session.session
./encrypt_session.py encrypt bot_session.session

# Sessions are automatically decrypted at runtime
# Set SESSION_ENCRYPTION_PASSWORD in .env or SSM
```

### Rate Limit Recovery
- **Exponential backoff**: Automatic retry with increasing delays on rate limits
- **FloodWaitError handling**: Respects Telegram's rate limit requirements
- **Google Sheets quota management**: Handles API quota limits with retry logic
- **Audit logging**: All rate limit incidents logged with timestamps

### Google API Retry Strategy
- **Quota detection**: Automatically detects and handles quota exceeded errors
- **Exponential backoff**: 60-second base delay for quota limits, 2-second for general errors
- **Fallback mechanisms**: Graceful degradation when APIs are unavailable
- **Structured logging**: Detailed error tracking and retry attempts

### Secrets Rotation Policy
- **AWS SSM Parameter Store**: Production secrets stored in encrypted parameters
- **Environment fallback**: Development support with .env files
- **Credential age monitoring**: Automatic warnings for credentials older than 90 days
- **Migration tools**: Easy migration from .env to SSM Parameter Store

```bash
# Migrate secrets to SSM Parameter Store
python3 secrets_loader.py migrate

# Test secrets configuration
python3 secrets_loader.py test

# Check secrets health
python3 secrets_loader.py health
```

### Backup and Restore Strategy
- **Automated backups**: Daily encrypted backups to S3 with local fallback
- **Session file backup**: Encrypted session files included in backups
- **Google Sheets export**: Automatic export of lead data with metadata
- **Retention policies**: Configurable retention (default: 30 days)
- **Restore tools**: Interactive and automated restore capabilities

```bash
# Manual backup
./backup.sh

# Test backup configuration
./backup.sh --test

# Restore from backup
./restore.sh --interactive

# Set up automated backups (cron)
sudo cp cron-backup.conf /etc/cron.d/telegram-bot-backup
```

### Network Security
- **Firewall**: UFW enabled with minimal rules (SSH from your IP only)
- **Security Groups**: Restricted inbound (SSH), limited outbound (HTTPS/DNS)
- **No open ports** except SSH from authorized IP
- **Fail2ban**: Automatic IP blocking for failed SSH attempts

### Application Security
- **Non-root execution**: Bot runs as `ubuntu` user
- **File permissions**: Strict 600/700 permissions on sensitive files
- **Process isolation**: Systemd security features enabled
- **Encrypted storage**: EBS volumes encrypted at rest
- **Structured logging**: Audit trails with no sensitive data exposure

### Access Control
- **Admin-only functions**: Restricted to your user/chat IDs
- **SSH key authentication**: No password login
- **Credential isolation**: Each team member uses separate credentials
- **Session isolation**: Encrypted sessions prevent unauthorized access

---

## Cost Optimization (FREE TIER)

### AWS Free Tier Limits
- **EC2**: 750 hours/month t2.micro (enough for 24/7)
- **EBS**: 30GB storage (we use 8GB)
- **Data Transfer**: 15GB outbound (plenty for bot traffic)
- **CloudWatch**: 10 custom metrics, 5GB logs

### Cost Monitoring
```bash
# Monitor your usage
./cost-monitor.sh

# Set up billing alerts (recommended)
aws budgets create-budget --account-id YOUR_ACCOUNT_ID \
  --budget file://budget-alert.json
```

---

## Testing Your Bot

### 1. Trigger Lead Flow
Send a message containing "hello from hacken" to any Telegram user

### 2. Expected Behavior
- Bot detects outgoing message
- Processes lead information
- Logs to Google Sheets
- Sends Hacken response message
- Shows admin buttons in your admin group

### 3. Admin Functions
- **Tag buttons**: Audit, Pen Test, Bug Bounty, Monitoring
- **Status update**: Cold → Warm
- **Group creation**: Auto-named with company/first name + logo
- **Export**: Lead data export

---

## Management & Monitoring

### Service Control
```bash
sudo systemctl start telegram-bot      # Start bot
sudo systemctl stop telegram-bot       # Stop bot
sudo systemctl restart telegram-bot    # Restart bot
sudo systemctl status telegram-bot     # Check status
```

### Monitoring Commands
```bash
sudo journalctl -u telegram-bot -f     # Live logs
/opt/telegram-bot/health-check.sh      # Health status
/opt/telegram-bot/monitor.sh           # System resources
./cost-monitor.sh                      # AWS costs
```

### Maintenance
```bash
/opt/telegram-bot/backup.sh            # Create backup
sudo apt update && sudo apt upgrade    # Update system
```

### Built-in Monitoring Features
- **Health checks**: Automated every 5 minutes
- **Resource monitoring**: CPU, memory, disk usage
- **Log rotation**: Prevents disk space issues with automatic cleanup
- **Cost tracking**: Free tier usage monitoring
- **Structured logging**: JSON logs with audit trails
- **Automated backups**: Daily encrypted backups with retention

### Remote Development Setup

#### Using VSCode Remote SSH (Recommended)
```bash
# Install VSCode Remote SSH extension
# Connect to your instance
ssh -i your-key.pem ubuntu@INSTANCE_IP

# In VSCode, use Command Palette (Ctrl+Shift+P)
# Type: "Remote-SSH: Connect to Host"
# Enter: ubuntu@INSTANCE_IP
# Select your private key file

# Open the bot directory
# File -> Open Folder -> /opt/telegram-bot
```

#### Using Vim (Terminal-based)
```bash
# SSH to your instance
ssh -i your-key.pem ubuntu@INSTANCE_IP

# Edit files with vim
vim /opt/telegram-bot/.env
vim /opt/telegram-bot/lead_tracker.py

# Vim basics:
# i = insert mode
# Esc = command mode
# :w = save
# :q = quit
# :wq = save and quit
```

---

## Troubleshooting

### Bot Not Starting
```bash
# Check structured logs
sudo tail -f /var/log/telegram-bot/telegram-bot.log

# Check systemd logs
sudo journalctl -u telegram-bot -n 50

# Verify environment and secrets
cat /opt/telegram-bot/.env
python3 /opt/telegram-bot/secrets_loader.py health

# Check session encryption
ls -la /opt/telegram-bot/*.session*

# Check permissions
ls -la /opt/telegram-bot/

# Test session decryption
python3 -c "from encrypt_session import SessionEncryption; print('Encryption module OK')"
```

### Session Encryption Issues
```bash
# Check if sessions are encrypted
ls -la *.session.enc

# Test decryption with password
./encrypt_session.py decrypt session.session.enc

# Verify encryption password in environment
echo $SESSION_ENCRYPTION_PASSWORD

# Re-encrypt sessions if needed
./encrypt_session.py encrypt session.session
```

### Backup and Restore Issues
```bash
# Test backup configuration
./backup.sh --test

# Check S3 access (if configured)
aws s3 ls s3://your-backup-bucket/

# Manual backup
./backup.sh

# List available backups
./restore.sh --list

# Check backup logs
tail -f /var/log/telegram-bot/backup.log
```

### Rate Limiting Issues
```bash
# Check for rate limit incidents in logs
grep "rate_limit_hit" /var/log/telegram-bot/telegram-bot.log

# Check Google Sheets quota issues
grep "sheets_quota_limit" /var/log/telegram-bot/telegram-bot.log

# Monitor API call patterns
grep "api_call_retry" /var/log/telegram-bot/telegram-bot.log
```

### Can't Connect to Instance
```bash
# Check your current IP
curl ifconfig.me

# Verify security group
aws ec2 describe-security-groups --group-ids sg-xxxxxxxxx

# Update security group if IP changed
aws ec2 authorize-security-group-ingress \
  --group-id sg-xxxxxxxxx \
  --protocol tcp --port 22 --cidr YOUR_NEW_IP/32
```

### High AWS Costs
```bash
# Monitor costs
./cost-monitor.sh

# Check billing dashboard
aws ce get-cost-and-usage --time-period Start=2024-01-01,End=2024-01-31 \
  --granularity MONTHLY --metrics BlendedCost

# Stop instance if needed
aws ec2 stop-instances --instance-ids i-xxxxxxxxx
```

---

## Security Checklist

### Before Deployment
- [ ] Use strong SSH key (RSA 4096 or Ed25519)
- [ ] Verify your IP address is correct
- [ ] Never share credentials in chat/email
- [ ] Use secure file transfer methods

### After Deployment
- [ ] Verify firewall status: `sudo ufw status`
- [ ] Check file permissions: `ls -la /opt/telegram-bot/`
- [ ] Test bot functionality
- [ ] Verify logs don't contain secrets
- [ ] Set up monitoring alerts

### Ongoing Security
- [ ] Regularly update system: `sudo apt update && sudo apt upgrade`
- [ ] Monitor AWS billing dashboard
- [ ] Rotate credentials every 90 days
- [ ] Review access logs monthly

---

## Team Collaboration

### Individual Deployments
- **Separate instances** per team member
- **Isolated environments** (no conflicts)
- **Personal credentials** (no sharing)
- **Individual cost tracking**

### Shared Resources
- **Documentation**: This guide and setup scripts
- **Templates**: Environment file templates
- **Scripts**: Automated deployment scripts
- **Monitoring**: Shared CloudWatch dashboard

### Best Practices
- **Naming convention**: `telegram-bot-YOURNAME`
- **Tagging**: Consistent AWS resource tags
- **Documentation**: Update this guide with improvements
- **Security**: Report security issues immediately

### What to Share
- All 15 files for complete deployment package
- `team-setup.sh` helps them get their own credentials
- Each person creates their own `.env.THEIRNAME` file
- Everyone gets their own isolated AWS instance

### What NOT to Share
- Your `.env` file (contains your secrets)
- Your `credentials.json` (your Google account)
- Session files (personal Telegram sessions)

---

## Success Criteria

Your deployment is successful when:
- [ ] Bot responds to trigger messages
- [ ] Admin buttons work in your admin chat
- [ ] Group creation works with all features
- [ ] Service auto-restarts on failure
- [ ] Logs are clean (no errors)
- [ ] AWS costs remain $0
- [ ] Security checklist completed

---

## Quick Reference Commands

### Initial Setup
```bash
# Get credentials interactively
chmod +x team-setup.sh
./team-setup.sh

# Deploy AWS infrastructure
aws cloudformation create-stack \
  --stack-name telegram-bot-YOURNAME \
  --template-body file://cloudformation-template.yaml \
  --parameters ParameterKey=KeyPairName,ParameterValue=YOUR_KEY \
               ParameterKey=AllowedSSHIP,ParameterValue=YOUR_IP/32 \
               ParameterKey=TeamMemberName,ParameterValue=YOURNAME \
  --capabilities CAPABILITY_IAM

# Wait for completion
aws cloudformation wait stack-create-complete --stack-name telegram-bot-YOURNAME

# Get instance IP
aws cloudformation describe-stacks \
  --stack-name telegram-bot-YOURNAME \
  --query 'Stacks[0].Outputs[?OutputKey==`InstanceIP`].OutputValue' \
  --output text

# Upload and deploy
./secure-upload.sh INSTANCE_IP your-key.pem .env.YOURNAME
ssh -i your-key.pem ubuntu@INSTANCE_IP
cd /home/<USER>/telegram-bot
chmod +x secure-deploy.sh
./secure-deploy.sh
sudo systemctl start telegram-bot
sudo systemctl enable telegram-bot
```

### Daily Operations
```bash
# Check bot status
sudo systemctl status telegram-bot

# View live logs
sudo journalctl -u telegram-bot -f

# Check system health
/opt/telegram-bot/health-check.sh

# Monitor AWS costs
./cost-monitor.sh

# Create backup
/opt/telegram-bot/backup.sh
```

### Troubleshooting
```bash
# Restart bot
sudo systemctl restart telegram-bot

# Check recent logs
sudo journalctl -u telegram-bot -n 50

# Verify configuration
cat /opt/telegram-bot/.env

# Check file permissions
ls -la /opt/telegram-bot/

# Monitor system resources
/opt/telegram-bot/monitor.sh

# Check your current IP
curl ifconfig.me
```

### Local Testing (Without AWS)
```bash
# Install dependencies
pip install -r requirements.txt

# Run locally
python lead_tracker.py
```

---

Your secure, production-ready Telegram bot is ready for deployment with enterprise-grade security, zero cost using AWS free tier, and complete team collaboration support.
