# Telegram Bot Backup Cron Configuration
# Add this to your crontab with: crontab -e
# Or install system-wide: sudo cp cron-backup.conf /etc/cron.d/telegram-bot-backup

# Environment variables for cron
SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin
MAILTO=""

# Backup schedule examples:
# Choose ONE of the following schedules based on your needs

# Option 1: Daily backup at 2:30 AM
30 2 * * * root /opt/telegram-bot/backup.sh >> /var/log/telegram-bot/backup.log 2>&1

# Option 2: Twice daily (6 AM and 6 PM)
# 0 6,18 * * * root /opt/telegram-bot/backup.sh >> /var/log/telegram-bot/backup.log 2>&1

# Option 3: Every 6 hours
# 0 */6 * * * root /opt/telegram-bot/backup.sh >> /var/log/telegram-bot/backup.log 2>&1

# Option 4: Weekly backup on Sundays at 3 AM
# 0 3 * * 0 root /opt/telegram-bot/backup.sh >> /var/log/telegram-bot/backup.log 2>&1

# Option 5: Business hours only (Mon-Fri, every 4 hours from 8 AM to 8 PM)
# 0 8,12,16,20 * * 1-5 root /opt/telegram-bot/backup.sh >> /var/log/telegram-bot/backup.log 2>&1

# Cleanup old logs weekly
0 3 * * 0 root find /var/log/telegram-bot -name "*.log" -mtime +30 -delete

# Health check - test backup configuration monthly
0 4 1 * * root /opt/telegram-bot/backup.sh --test >> /var/log/telegram-bot/backup-test.log 2>&1
