AWSTemplateFormatVersion: '2010-09-09'
Description: 'Secure Telegram Bot Infrastructure - Production Ready with Hardening'

Metadata:
  AWS::CloudFormation::Interface:
    ParameterGroups:
      - Label:
          default: "Infrastructure Configuration"
        Parameters:
          - Environment
          - TeamMemberName
          - ProjectName
      - Label:
          default: "Security Configuration"
        Parameters:
          - KeyPairName
          - AllowedSSHIP
      - Label:
          default: "Backup Configuration"
        Parameters:
          - BackupS3Bucket
          - EnableBackups

Parameters:
  KeyPairName:
    Type: AWS::EC2::KeyPair::KeyName
    Description: Name of an existing EC2 KeyPair for SSH access

  AllowedSSHIP:
    Type: String
    Description: Your current IP address for SSH access (get from whatismyip.com)
    AllowedPattern: '^([0-9]{1,3}\.){3}[0-9]{1,3}/32$'
    ConstraintDescription: Must be a valid IP address in CIDR format (e.g., *******/32)

  TeamMemberName:
    Type: String
    Description: Your name for resource tagging (e.g., john, sarah)
    AllowedPattern: '^[a-zA-Z0-9]+$'
    ConstraintDescription: Only letters and numbers allowed
    MaxLength: 20

  Environment:
    Type: String
    Description: Environment name (dev, staging, prod)
    Default: prod
    AllowedValues:
      - dev
      - staging
      - prod

  ProjectName:
    Type: String
    Description: Project name for resource naming
    Default: telegram-bot
    AllowedPattern: '^[a-zA-Z0-9-]+$'
    ConstraintDescription: Only letters, numbers, and hyphens allowed
    MaxLength: 30

  BackupS3Bucket:
    Type: String
    Description: S3 bucket name for backups (optional, leave empty to skip)
    Default: ""

  EnableBackups:
    Type: String
    Description: Enable automated backups
    Default: "true"
    AllowedValues:
      - "true"
      - "false"

Conditions:
  EnableBackupsCondition: !Equals [!Ref EnableBackups, "true"]
  HasBackupBucket: !Not [!Equals [!Ref BackupS3Bucket, ""]]

Resources:
  # IAM Role for EC2 Instance with enhanced permissions
  TelegramBotRole:
    Type: AWS::IAM::Role
    DeletionPolicy: Retain
    Properties:
      RoleName: !Sub '${ProjectName}-role-${TeamMemberName}-${Environment}-${AWS::Region}'
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: ec2.amazonaws.com
            Action: sts:AssumeRole
      Policies:
        - PolicyName: SSMParameterAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - ssm:GetParameter
                  - ssm:GetParameters
                  - ssm:PutParameter
                  - ssm:DeleteParameter
                  - ssm:DescribeParameters
                Resource:
                  - !Sub 'arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${ProjectName}/*'
        - !If
          - HasBackupBucket
          - PolicyName: S3BackupAccess
            PolicyDocument:
              Version: '2012-10-17'
              Statement:
                - Effect: Allow
                  Action:
                    - s3:PutObject
                    - s3:GetObject
                    - s3:DeleteObject
                    - s3:ListBucket
                  Resource:
                    - !Sub 'arn:aws:s3:::${BackupS3Bucket}'
                    - !Sub 'arn:aws:s3:::${BackupS3Bucket}/*'
          - !Ref AWS::NoValue
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-role-${TeamMemberName}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: !Ref ProjectName
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Component
          Value: iam-role
        - Key: CreatedBy
          Value: CloudFormation
        - Key: CreatedDate
          Value: !Sub '${AWS::StackName}-${AWS::Region}'

  # Instance Profile
  TelegramBotInstanceProfile:
    Type: AWS::IAM::InstanceProfile
    DeletionPolicy: Retain
    Properties:
      InstanceProfileName: !Sub '${ProjectName}-profile-${TeamMemberName}-${Environment}'
      Roles:
        - !Ref TelegramBotRole
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-profile-${TeamMemberName}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: !Ref ProjectName
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Component
          Value: instance-profile

  # Security Group (Maximum security)
  TelegramBotSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    DeletionPolicy: Retain
    Properties:
      GroupName: !Sub '${ProjectName}-sg-${TeamMemberName}-${Environment}-${AWS::Region}'
      GroupDescription: !Sub 'Secure ${ProjectName} Security Group for ${TeamMemberName} in ${Environment}'
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 22
          ToPort: 22
          CidrIp: !Ref AllowedSSHIP
          Description: SSH access from team member IP only
      SecurityGroupEgress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: 0.0.0.0/0
          Description: HTTPS for Telegram API and AWS services
        - IpProtocol: tcp
          FromPort: 80
          ToPort: 80
          CidrIp: 0.0.0.0/0
          Description: HTTP for system updates
        - IpProtocol: tcp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
          Description: DNS TCP
        - IpProtocol: udp
          FromPort: 53
          ToPort: 53
          CidrIp: 0.0.0.0/0
          Description: DNS UDP
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-sg-${TeamMemberName}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: !Ref ProjectName
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Component
          Value: security-group
        - Key: CreatedBy
          Value: CloudFormation
        - Key: CreatedDate
          Value: !Sub '${AWS::StackName}-${AWS::Region}'

  # EC2 Instance (Production Ready)
  TelegramBotInstance:
    Type: AWS::EC2::Instance
    DeletionPolicy: Retain
    Properties:
      ImageId: ami-0c02fb55956c7d316  # Ubuntu 22.04 LTS (update for your region)
      InstanceType: t2.micro  # FREE TIER ELIGIBLE
      KeyName: !Ref KeyPairName
      IamInstanceProfile: !Ref TelegramBotInstanceProfile
      SecurityGroupIds:
        - !Ref TelegramBotSecurityGroup
      BlockDeviceMappings:
        - DeviceName: /dev/sda1
          Ebs:
            VolumeSize: 8  # FREE TIER: 30GB total allowed
            VolumeType: gp2  # FREE TIER: gp2 only
            Encrypted: true
            DeleteOnTermination: false  # Retain data on termination
      UserData:
        Fn::Base64: !Sub |
          #!/bin/bash
          # Enhanced setup for production deployment
          apt-get update
          apt-get install -y python3 python3-pip python3-venv git curl awscli gpg fail2ban ufw logrotate

          # Create application directories with proper permissions
          mkdir -p /opt/telegram-bot /var/log/telegram-bot /opt/telegram-bot/backups
          chown ubuntu:ubuntu /opt/telegram-bot /opt/telegram-bot/backups
          chown syslog:adm /var/log/telegram-bot
          chmod 755 /var/log/telegram-bot

          # Configure firewall
          ufw --force enable
          ufw default deny incoming
          ufw default allow outgoing
          ufw allow from ${AllowedSSHIP} to any port 22

          # Configure fail2ban
          systemctl enable fail2ban
          systemctl start fail2ban

          # Configure automatic security updates
          apt-get install -y unattended-upgrades
          echo 'Unattended-Upgrade::Automatic-Reboot "false";' >> /etc/apt/apt.conf.d/50unattended-upgrades

          # Set up SSM parameters prefix
          aws ssm put-parameter --region ${AWS::Region} --name "/${ProjectName}/config/initialized" --value "true" --type "String" --overwrite || true

          # Create ready indicator
          touch /home/<USER>/instance-ready
          chown ubuntu:ubuntu /home/<USER>/instance-ready
          echo "Instance initialized at $(date)" > /home/<USER>/instance-ready

      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-instance-${TeamMemberName}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: !Ref ProjectName
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Component
          Value: ec2-instance
        - Key: CreatedBy
          Value: CloudFormation
        - Key: CreatedDate
          Value: !Sub '${AWS::StackName}-${AWS::Region}'
        - Key: BackupEnabled
          Value: !Ref EnableBackups
        - Key: CostCenter
          Value: !Ref Environment

  # Elastic IP (FREE TIER: 1 free when attached)
  TelegramBotEIP:
    Type: AWS::EC2::EIP
    DeletionPolicy: Retain
    Properties:
      InstanceId: !Ref TelegramBotInstance
      Tags:
        - Key: Name
          Value: !Sub '${ProjectName}-eip-${TeamMemberName}'
        - Key: Environment
          Value: !Ref Environment
        - Key: Project
          Value: !Ref ProjectName
        - Key: Owner
          Value: !Ref TeamMemberName
        - Key: Component
          Value: elastic-ip
        - Key: CreatedBy
          Value: CloudFormation

Outputs:
  StackName:
    Description: Name of the CloudFormation stack
    Value: !Ref AWS::StackName
    Export:
      Name: !Sub '${AWS::StackName}-StackName'

  InstanceId:
    Description: Instance ID of the Telegram Bot server
    Value: !Ref TelegramBotInstance
    Export:
      Name: !Sub '${AWS::StackName}-InstanceId'

  InstanceIP:
    Description: Public IP address of the Telegram Bot server
    Value: !Ref TelegramBotEIP
    Export:
      Name: !Sub '${AWS::StackName}-PublicIP'

  SSHCommand:
    Description: SSH command to connect to the instance
    Value: !Sub 'ssh -i ${KeyPairName}.pem ubuntu@${TelegramBotEIP}'

  UploadCommand:
    Description: Command to upload files securely
    Value: !Sub './secure-upload.sh ${TelegramBotEIP} ${KeyPairName}.pem .env.${TeamMemberName}'

  SecurityGroupId:
    Description: Security Group ID
    Value: !Ref TelegramBotSecurityGroup
    Export:
      Name: !Sub '${AWS::StackName}-SecurityGroupId'

  IAMRoleArn:
    Description: IAM Role ARN for the instance
    Value: !GetAtt TelegramBotRole.Arn
    Export:
      Name: !Sub '${AWS::StackName}-IAMRoleArn'

  SSMParameterPrefix:
    Description: SSM Parameter Store prefix for secrets
    Value: !Sub '/${ProjectName}/'

  BackupConfiguration:
    Description: Backup configuration status
    Value: !If
      - EnableBackupsCondition
      - !If
        - HasBackupBucket
        - !Sub 'Enabled with S3 bucket: ${BackupS3Bucket}'
        - 'Enabled (local backups only)'
      - 'Disabled'

  Environment:
    Description: Environment name
    Value: !Ref Environment

  ProjectName:
    Description: Project name
    Value: !Ref ProjectName

  TeamMember:
    Description: Team member name for this deployment
    Value: !Ref TeamMemberName

  Region:
    Description: AWS Region
    Value: !Ref AWS::Region

  CreationTime:
    Description: Stack creation timestamp
    Value: !Sub '${AWS::StackName} created in ${AWS::Region}'

  CostEstimate:
    Description: Estimated monthly cost (FREE TIER)
    Value: '$0.00 (Free Tier Eligible for 12 months)'

  NextSteps:
    Description: Next steps for deployment
    Value: 'Run: ./secure-upload.sh && ssh to instance && ./secure-deploy.sh'
