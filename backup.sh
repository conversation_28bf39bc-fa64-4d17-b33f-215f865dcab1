#!/bin/bash

# Telegram Bot Backup Script
# Automated backup of critical bot data to S3 with encryption and rotation
# Supports both S3 upload and email fallback

set -euo pipefail

# Configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly APP_DIR="/opt/telegram-bot"
readonly BACKUP_DIR="${APP_DIR}/backups"
readonly DATE=$(date +%Y%m%d_%H%M%S)
readonly BACKUP_NAME="telegram-bot-backup-${DATE}"
readonly BACKUP_FILE="${BACKUP_NAME}.tar.gz"
readonly ENCRYPTED_BACKUP="${BACKUP_NAME}.tar.gz.gpg"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "${BACKUP_DIR}/backup.log"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" | tee -a "${BACKUP_DIR}/backup.log"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "${BACKUP_DIR}/backup.log"
    exit 1
}

# Load environment variables
load_config() {
    if [[ -f "${APP_DIR}/.env" ]]; then
        source "${APP_DIR}/.env"
    elif [[ -f "${SCRIPT_DIR}/.env" ]]; then
        source "${SCRIPT_DIR}/.env"
    else
        warn "No .env file found, using environment variables"
    fi
    
    # Set defaults
    BACKUP_S3_BUCKET="${BACKUP_S3_BUCKET:-}"
    BACKUP_EMAIL="${BACKUP_EMAIL:-}"
    BACKUP_ENCRYPTION_KEY="${BACKUP_ENCRYPTION_KEY:-}"
    BACKUP_RETENTION_DAYS="${BACKUP_RETENTION_DAYS:-30}"
    AWS_REGION="${AWS_REGION:-us-east-1}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Create backup directory
    mkdir -p "${BACKUP_DIR}"
    
    # Check if running from correct location
    if [[ ! -d "${APP_DIR}" ]]; then
        error "Application directory not found: ${APP_DIR}"
    fi
    
    # Check AWS CLI if S3 backup is configured
    if [[ -n "${BACKUP_S3_BUCKET}" ]]; then
        if ! command -v aws &> /dev/null; then
            error "AWS CLI not found but S3 backup is configured"
        fi
        
        # Test AWS credentials
        if ! aws sts get-caller-identity &> /dev/null; then
            error "AWS credentials not configured or invalid"
        fi
    fi
    
    # Check GPG if encryption is configured
    if [[ -n "${BACKUP_ENCRYPTION_KEY}" ]]; then
        if ! command -v gpg &> /dev/null; then
            error "GPG not found but encryption is configured"
        fi
    fi
    
    log "Prerequisites check passed"
}

# Create backup archive
create_backup() {
    log "Creating backup archive..."
    
    cd "${APP_DIR}"
    
    # Files to backup
    local files_to_backup=(
        "storage.json"
        "*.session.enc"
        "credentials.json"
        ".env"
        "*.log"
    )
    
    # Create temporary file list
    local temp_file_list=$(mktemp)
    
    # Find files that exist
    for pattern in "${files_to_backup[@]}"; do
        find . -maxdepth 1 -name "${pattern}" -type f 2>/dev/null >> "${temp_file_list}" || true
    done
    
    # Check if we have files to backup
    if [[ ! -s "${temp_file_list}" ]]; then
        warn "No files found to backup"
        rm "${temp_file_list}"
        return 1
    fi
    
    # Create archive
    tar -czf "${BACKUP_DIR}/${BACKUP_FILE}" \
        --files-from="${temp_file_list}" \
        --transform 's,^\./,,' \
        2>/dev/null || {
        error "Failed to create backup archive"
    }
    
    rm "${temp_file_list}"
    
    # Verify archive
    if [[ ! -f "${BACKUP_DIR}/${BACKUP_FILE}" ]]; then
        error "Backup file was not created"
    fi
    
    local backup_size=$(du -h "${BACKUP_DIR}/${BACKUP_FILE}" | cut -f1)
    log "Backup archive created: ${BACKUP_FILE} (${backup_size})"
}

# Export Google Sheets data
export_sheets_data() {
    log "Exporting Google Sheets data..."
    
    # Try to export sheets data using Python
    if [[ -f "${APP_DIR}/google_sheets.py" ]]; then
        cd "${APP_DIR}"
        
        # Create sheets export script
        cat > "${BACKUP_DIR}/export_sheets.py" << 'EOF'
#!/usr/bin/env python3
import sys
import json
import csv
from datetime import datetime
try:
    from google_sheets import sheet, get_existing_user_ids
    
    # Export all data
    all_rows = sheet.get_all_values()
    
    # Save as CSV
    with open('sheets_backup.csv', 'w', newline='', encoding='utf-8') as f:
        writer = csv.writer(f)
        writer.writerows(all_rows)
    
    # Save metadata
    metadata = {
        'export_time': datetime.now().isoformat(),
        'total_rows': len(all_rows),
        'total_leads': len(get_existing_user_ids())
    }
    
    with open('sheets_metadata.json', 'w') as f:
        json.dump(metadata, f, indent=2)
    
    print(f"Exported {len(all_rows)} rows")
    
except Exception as e:
    print(f"Failed to export sheets: {e}")
    sys.exit(1)
EOF
        
        # Run export
        if python3 "${BACKUP_DIR}/export_sheets.py" 2>/dev/null; then
            # Add sheets data to backup
            if [[ -f "sheets_backup.csv" && -f "sheets_metadata.json" ]]; then
                tar -rf "${BACKUP_DIR}/${BACKUP_FILE}" sheets_backup.csv sheets_metadata.json
                gzip "${BACKUP_DIR}/${BACKUP_FILE%%.gz}"
                rm sheets_backup.csv sheets_metadata.json
                log "Google Sheets data exported and added to backup"
            fi
        else
            warn "Failed to export Google Sheets data"
        fi
        
        rm "${BACKUP_DIR}/export_sheets.py"
    else
        warn "Google Sheets module not found, skipping export"
    fi
}

# Encrypt backup if configured
encrypt_backup() {
    if [[ -z "${BACKUP_ENCRYPTION_KEY}" ]]; then
        log "No encryption key configured, skipping encryption"
        return 0
    fi
    
    log "Encrypting backup..."
    
    # Encrypt using GPG
    gpg --batch --yes --trust-model always \
        --cipher-algo AES256 \
        --compress-algo 2 \
        --symmetric \
        --passphrase "${BACKUP_ENCRYPTION_KEY}" \
        --output "${BACKUP_DIR}/${ENCRYPTED_BACKUP}" \
        "${BACKUP_DIR}/${BACKUP_FILE}" || {
        error "Failed to encrypt backup"
    }
    
    # Remove unencrypted backup
    rm "${BACKUP_DIR}/${BACKUP_FILE}"
    
    local encrypted_size=$(du -h "${BACKUP_DIR}/${ENCRYPTED_BACKUP}" | cut -f1)
    log "Backup encrypted: ${ENCRYPTED_BACKUP} (${encrypted_size})"
}

# Upload to S3
upload_to_s3() {
    if [[ -z "${BACKUP_S3_BUCKET}" ]]; then
        log "No S3 bucket configured, skipping S3 upload"
        return 0
    fi
    
    log "Uploading backup to S3..."
    
    local backup_file_to_upload
    if [[ -f "${BACKUP_DIR}/${ENCRYPTED_BACKUP}" ]]; then
        backup_file_to_upload="${ENCRYPTED_BACKUP}"
    else
        backup_file_to_upload="${BACKUP_FILE}"
    fi
    
    # Upload to S3 with server-side encryption
    aws s3 cp "${BACKUP_DIR}/${backup_file_to_upload}" \
        "s3://${BACKUP_S3_BUCKET}/telegram-bot-backups/${backup_file_to_upload}" \
        --region "${AWS_REGION}" \
        --server-side-encryption AES256 \
        --metadata "backup-date=${DATE},app=telegram-bot" || {
        error "Failed to upload backup to S3"
    }
    
    log "Backup uploaded to S3: s3://${BACKUP_S3_BUCKET}/telegram-bot-backups/${backup_file_to_upload}"
}

# Send backup via email (fallback)
email_backup() {
    if [[ -z "${BACKUP_EMAIL}" ]]; then
        log "No backup email configured, skipping email"
        return 0
    fi
    
    log "Sending backup via email..."
    
    local backup_file_to_email
    if [[ -f "${BACKUP_DIR}/${ENCRYPTED_BACKUP}" ]]; then
        backup_file_to_email="${ENCRYPTED_BACKUP}"
    else
        backup_file_to_email="${BACKUP_FILE}"
    fi
    
    local backup_size=$(du -h "${BACKUP_DIR}/${backup_file_to_email}" | cut -f1)
    
    # Check file size (email attachment limit)
    local size_bytes=$(stat -f%z "${BACKUP_DIR}/${backup_file_to_email}" 2>/dev/null || stat -c%s "${BACKUP_DIR}/${backup_file_to_email}")
    if [[ ${size_bytes} -gt 25000000 ]]; then  # 25MB limit
        warn "Backup file too large for email (${backup_size}), skipping email"
        return 0
    fi
    
    # Send email using mail command
    if command -v mail &> /dev/null; then
        echo "Telegram Bot backup created on $(date)" | \
        mail -s "Telegram Bot Backup - ${DATE}" \
             -a "${BACKUP_DIR}/${backup_file_to_email}" \
             "${BACKUP_EMAIL}" && {
            log "Backup sent via email to ${BACKUP_EMAIL}"
        } || {
            warn "Failed to send backup via email"
        }
    else
        warn "Mail command not available, cannot send email backup"
    fi
}

# Clean old backups
cleanup_old_backups() {
    log "Cleaning up old backups..."
    
    # Clean local backups older than retention period
    find "${BACKUP_DIR}" -name "telegram-bot-backup-*.tar.gz*" \
         -type f -mtime +${BACKUP_RETENTION_DAYS} -delete 2>/dev/null || true
    
    # Clean S3 backups if configured
    if [[ -n "${BACKUP_S3_BUCKET}" ]]; then
        local cutoff_date=$(date -d "${BACKUP_RETENTION_DAYS} days ago" +%Y%m%d 2>/dev/null || \
                           date -v-${BACKUP_RETENTION_DAYS}d +%Y%m%d 2>/dev/null || \
                           echo "20200101")
        
        aws s3 ls "s3://${BACKUP_S3_BUCKET}/telegram-bot-backups/" \
            --region "${AWS_REGION}" | \
        while read -r line; do
            local file_date=$(echo "$line" | awk '{print $4}' | grep -o '[0-9]\{8\}' | head -1)
            local file_name=$(echo "$line" | awk '{print $4}')
            
            if [[ -n "${file_date}" && "${file_date}" < "${cutoff_date}" ]]; then
                aws s3 rm "s3://${BACKUP_S3_BUCKET}/telegram-bot-backups/${file_name}" \
                    --region "${AWS_REGION}" || true
                log "Deleted old S3 backup: ${file_name}"
            fi
        done 2>/dev/null || true
    fi
    
    log "Cleanup completed"
}

# Main function
main() {
    log "Starting Telegram Bot backup process..."
    
    load_config
    check_prerequisites
    create_backup
    export_sheets_data
    encrypt_backup
    upload_to_s3
    email_backup
    cleanup_old_backups
    
    log "Backup process completed successfully!"
    
    # Print summary
    echo ""
    echo "=== Backup Summary ==="
    echo "Date: ${DATE}"
    echo "Local backup: ${BACKUP_DIR}/"
    if [[ -n "${BACKUP_S3_BUCKET}" ]]; then
        echo "S3 backup: s3://${BACKUP_S3_BUCKET}/telegram-bot-backups/"
    fi
    if [[ -n "${BACKUP_EMAIL}" ]]; then
        echo "Email backup: ${BACKUP_EMAIL}"
    fi
    echo "Retention: ${BACKUP_RETENTION_DAYS} days"
    echo "======================"
}

# Handle script arguments
case "${1:-}" in
    --help|-h)
        echo "Usage: $0 [--help|--test|--restore]"
        echo "  --help    Show this help message"
        echo "  --test    Test backup configuration without creating backup"
        echo "  --restore Restore from latest backup (interactive)"
        exit 0
        ;;
    --test)
        log "Testing backup configuration..."
        load_config
        check_prerequisites
        log "Configuration test passed!"
        exit 0
        ;;
    --restore)
        echo "Restore functionality not implemented yet"
        exit 1
        ;;
    "")
        main
        ;;
    *)
        error "Unknown argument: $1. Use --help for usage information."
        ;;
esac
