from telethon.tl.functions.messages import CreateChatRequest, ExportChatInviteRequest, EditChatTitleRequest
from telethon.tl.functions.contacts import ResolveUsernameRequest
from telethon.tl.functions.messages import AddChatUserRequest
from telethon import functions
from telethon.errors import FloodWaitError
from google_sheets import sheet, get_lead_data
from secrets_loader import get_secret
import asyncio
import os
import logging

# Configure structured logging
from logging_config import log_admin_action, log_user_action
logger = logging.getLogger(__name__)

async def telegram_api_call_with_retry(func, *args, max_retries=3, **kwargs):
    """Wrapper for Telegram API calls with exponential backoff on rate limits"""
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except FloodWaitError as e:
            wait_time = e.seconds
            logger.warning(
                f"Rate limit hit, waiting {wait_time}s (attempt {attempt + 1}/{max_retries})",
                extra={
                    'action': 'rate_limit_hit',
                    'wait_time': wait_time,
                    'attempt': attempt + 1,
                    'function': func.__name__
                }
            )
            if attempt < max_retries - 1:
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"Max retries exceeded for {func.__name__}")
                raise
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(
                    f"API call failed, retrying in {wait_time}s: {e}",
                    extra={
                        'action': 'api_call_retry',
                        'wait_time': wait_time,
                        'attempt': attempt + 1,
                        'error': str(e)
                    }
                )
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"API call failed after {max_retries} attempts: {e}")
                raise

async def create_lead_group(client, display_name, username, user_id):
    """
    Enhanced group creation with:
    - Smart naming (company name or first name)
    - Logo setting
    - Summary message to admin
    - Rate limiting and structured logging
    """
    try:
        admin_user_id = int(get_secret("ADMIN_USER_ID"))

        logger.info(
            f"Starting group creation for user {user_id}",
            extra={
                'action': 'group_creation_start',
                'user_id': user_id,
                'username': username,
                'display_name': display_name
            }
        )

        # Get lead data from Google Sheets
        lead_data = get_lead_data(user_id)
        if not lead_data:
            logger.warning(
                f"No lead data found for user {user_id}",
                extra={
                    'action': 'lead_data_not_found',
                    'user_id': user_id
                }
            )
            lead_data = {
                'full_name': display_name,
                'username': username,
                'company': '',
                'tags': '',
                'bio': '',
                'message': ''
            }

        # Step 1: Determine group name (company name or first name)
        company_name = lead_data.get('company', '').strip()
        first_name = lead_data.get('full_name', display_name).split()[0] if lead_data.get('full_name', display_name) else display_name

        if company_name:
            group_title = f"{company_name} / Hacken"
            logger.info(
                f"Using company name for group: {company_name}",
                extra={
                    'action': 'group_name_company',
                    'company_name': company_name,
                    'user_id': user_id
                }
            )
        else:
            group_title = f"{first_name} / Hacken"
            logger.info(
                f"Using first name for group: {first_name}",
                extra={
                    'action': 'group_name_first_name',
                    'first_name': first_name,
                    'user_id': user_id
                }
            )

        # Step 2: Create group with only admin
        result = await telegram_api_call_with_retry(
            client,
            CreateChatRequest(users=[admin_user_id], title="Temp")
        )
        # Handle different response types
        group_id = None
        if hasattr(result, 'chats') and result.chats:
            group = result.chats[0]
            group_id = group.id
        elif hasattr(result, 'id'):
            group_id = result.id
        elif hasattr(result, 'updates') and result.updates:
            # Extract group ID from updates
            try:
                for update in result.updates:
                    if hasattr(update, 'message') and hasattr(update.message, 'peer_id'):
                        if hasattr(update.message.peer_id, 'chat_id'):
                            group_id = update.message.peer_id.chat_id
                            break
                        elif hasattr(update.message.peer_id, 'channel_id'):
                            group_id = update.message.peer_id.channel_id
                            break
            except Exception as e:
                logger.warning(f"Could not iterate updates: {e}")

        if not group_id:
            raise Exception(f"Could not extract group ID from response type: {type(result)}")
        logger.info(
            f"Created group with ID: {group_id}",
            extra={
                'action': 'group_created',
                'group_id': group_id,
                'user_id': user_id
            }
        )

        # Step 3: Rename group to proper title
        await telegram_api_call_with_retry(
            client,
            EditChatTitleRequest(chat_id=group_id, title=group_title)
        )
        logger.info(
            f"Renamed group to: {group_title}",
            extra={
                'action': 'group_renamed',
                'group_id': group_id,
                'group_title': group_title,
                'user_id': user_id
            }
        )

        # Step 4: Set group logo
        logo_path = "Logo.jpg"
        if os.path.exists(logo_path):
            try:
                # Upload the logo as group photo
                await telegram_api_call_with_retry(
                    client,
                    functions.messages.EditChatPhotoRequest(
                        chat_id=group_id,
                        photo=await client.upload_file(logo_path)
                    )
                )
                logger.info(
                    "✅ Group logo set successfully",
                    extra={
                        'action': 'group_logo_set',
                        'group_id': group_id,
                        'user_id': user_id
                    }
                )
            except Exception as e:
                logger.warning(
                    f"Failed to set group logo: {e}",
                    extra={
                        'action': 'group_logo_failed',
                        'group_id': group_id,
                        'user_id': user_id,
                        'error': str(e)
                    }
                )
        else:
            logger.warning(
                f"Logo file not found: {logo_path}",
                extra={
                    'action': 'logo_file_not_found',
                    'logo_path': logo_path,
                    'user_id': user_id
                }
            )

        # Step 5: Try to add lead user
        try:
            await telegram_api_call_with_retry(
                client,
                AddChatUserRequest(chat_id=group_id, user_id=user_id, fwd_limit=10)
            )
            added = True
            logger.info(
                f"✅ User {user_id} added to group directly",
                extra={
                    'action': 'user_added_directly',
                    'group_id': group_id,
                    'user_id': user_id
                }
            )
        except Exception as e:
            logger.warning(
                f"Could not add user directly: {e}",
                extra={
                    'action': 'user_add_failed',
                    'group_id': group_id,
                    'user_id': user_id,
                    'error': str(e)
                }
            )
            added = False

        # Step 6: Generate invite link (always generate for summary)
        invite_link = ""
        try:
            invite = await telegram_api_call_with_retry(
                client,
                ExportChatInviteRequest(group_id)
            )
            invite_link = invite.link
            logger.info(
                "✅ Invite link generated",
                extra={
                    'action': 'invite_link_generated',
                    'group_id': group_id,
                    'user_id': user_id
                }
            )

            # If user wasn't added directly, send them the invite
            if not added:
                await telegram_api_call_with_retry(
                    client.send_message,
                    user_id,
                    f"Couldn't add you directly — here's your private group: {invite_link}"
                )
        except Exception as e:
            logger.error(
                f"Failed to generate invite link: {e}",
                extra={
                    'action': 'invite_link_failed',
                    'group_id': group_id,
                    'user_id': user_id,
                    'error': str(e)
                }
            )

        # Step 7: Update Google Sheet with group status
        all_rows = sheet.get_all_values()
        headers = all_rows[0]
        for i, row in enumerate(all_rows[1:], start=2):
            if row[0] == str(user_id):
                sheet.update_cell(i, headers.index("Group Created") + 1, "TRUE")
                break

        # Step 8: Send welcome message in group
        await asyncio.sleep(1)
        await telegram_api_call_with_retry(
            client.send_message,
            group_id,
            "Welcome to Hacken – your gateway to Web3 security.\n\n"
            "Since 2017, we've been securing the blockchain space with smart contract audits, "
            "real-time threat detection, and on-chain monitoring.\n\n"
            "🔐 Explore Our Ecosystem:\n"
            "Audits & Services: https://hacken.io\n"
            "Exchange Ratings: https://cer.live\n"
            "Blockchain Intel: https://extractor.live\n"
            "Verified Data: https://trustarmy.io\n\n"
            "Let's build a safer Web3 together."
        )

        # Step 9: Send summary message to admin
        await send_group_summary(client, admin_user_id, group_title, invite_link, lead_data)

        logger.info(
            f"Group creation completed successfully for user {user_id}",
            extra={
                'action': 'group_creation_complete',
                'group_id': group_id,
                'group_title': group_title,
                'user_id': user_id,
                'invite_link': invite_link
            }
        )

        # Log admin action for audit trail
        log_admin_action(
            'group_created',
            admin_user_id,
            user_id,
            group_id=group_id,
            group_title=group_title,
            company=lead_data.get('company', ''),
            invite_link=invite_link
        )

        return True

    except Exception as e:
        logger.error(
            f"Group creation failed: {e}",
            extra={
                'action': 'group_creation_failed',
                'user_id': user_id,
                'error': str(e)
            }
        )
        return False

async def send_group_summary(client, admin_user_id, group_name, group_link, lead_data):
    """Send a summary message to admin with group details"""
    try:
        tags = lead_data.get('tags', 'None')
        full_name = lead_data.get('full_name', 'Unknown')
        username = lead_data.get('username', 'N/A')
        company = lead_data.get('company', 'N/A')

        summary_message = f"""🎉 **Group Created Successfully!**

👥 **Group Name**: {group_name}
🔗 **Group Link**: {group_link}

👤 **Lead Details**:
• **Name**: {full_name}
• **Username**: @{username}
• **Company**: {company}
• **Tags**: {tags}

✅ Group is ready for engagement!"""

        await telegram_api_call_with_retry(
            client.send_message,
            admin_user_id,
            summary_message
        )
        logger.info(
            "✅ Group summary sent to admin",
            extra={
                'action': 'group_summary_sent',
                'admin_user_id': admin_user_id,
                'group_name': group_name,
                'lead_name': full_name
            }
        )

    except Exception as e:
        logger.error(
            f"Failed to send group summary: {e}",
            extra={
                'action': 'group_summary_failed',
                'admin_user_id': admin_user_id,
                'group_name': group_name,
                'error': str(e)
            }
        )
