#!/usr/bin/env python3
"""
Telegram Session Encryption/Decryption Tool
Encrypts .session files at rest using AES-256-GCM for production security
"""

import os
import sys
import getpass
import hashlib
import secrets
from pathlib import Path
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import argparse
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SessionEncryption:
    """Handles encryption/decryption of Telegram session files"""
    
    def __init__(self):
        self.backend = default_backend()
    
    def derive_key(self, password: str, salt: bytes) -> bytes:
        """Derive encryption key from password using PBKDF2"""
        return hashlib.pbkdf2_hmac('sha256', password.encode(), salt, 100000)
    
    def encrypt_file(self, file_path: str, password: str) -> bool:
        """Encrypt a session file"""
        try:
            session_path = Path(file_path)
            if not session_path.exists():
                logger.error(f"Session file not found: {file_path}")
                return False
            
            # Read original session file
            with open(session_path, 'rb') as f:
                plaintext = f.read()
            
            # Generate salt and IV
            salt = secrets.token_bytes(32)
            iv = secrets.token_bytes(12)  # GCM mode uses 12-byte IV
            
            # Derive key
            key = self.derive_key(password, salt)
            
            # Encrypt
            cipher = Cipher(algorithms.AES(key), modes.GCM(iv), backend=self.backend)
            encryptor = cipher.encryptor()
            ciphertext = encryptor.update(plaintext) + encryptor.finalize()
            
            # Create encrypted file
            encrypted_path = session_path.with_suffix('.session.enc')
            with open(encrypted_path, 'wb') as f:
                f.write(salt)  # First 32 bytes: salt
                f.write(iv)    # Next 12 bytes: IV
                f.write(encryptor.tag)  # Next 16 bytes: authentication tag
                f.write(ciphertext)     # Rest: encrypted data
            
            # Set secure permissions
            os.chmod(encrypted_path, 0o600)
            
            logger.info(f"Session encrypted: {encrypted_path}")
            
            # Optionally remove original (ask user)
            response = input(f"Remove original session file {session_path}? (y/N): ")
            if response.lower() == 'y':
                os.remove(session_path)
                logger.info(f"Original session file removed: {session_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            return False
    
    def decrypt_file(self, encrypted_path: str, password: str, output_path: str = None) -> bool:
        """Decrypt a session file"""
        try:
            enc_path = Path(encrypted_path)
            if not enc_path.exists():
                logger.error(f"Encrypted file not found: {encrypted_path}")
                return False
            
            # Read encrypted file
            with open(enc_path, 'rb') as f:
                salt = f.read(32)
                iv = f.read(12)
                tag = f.read(16)
                ciphertext = f.read()
            
            # Derive key
            key = self.derive_key(password, salt)
            
            # Decrypt
            cipher = Cipher(algorithms.AES(key), modes.GCM(iv, tag), backend=self.backend)
            decryptor = cipher.decryptor()
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            # Write decrypted file
            if output_path is None:
                output_path = str(enc_path).replace('.session.enc', '.session')
            
            with open(output_path, 'wb') as f:
                f.write(plaintext)
            
            # Set secure permissions
            os.chmod(output_path, 0o600)
            
            logger.info(f"Session decrypted: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            return False
    
    def decrypt_to_memory(self, encrypted_path: str, password: str) -> bytes:
        """Decrypt session file directly to memory (for runtime use)"""
        try:
            enc_path = Path(encrypted_path)
            if not enc_path.exists():
                raise FileNotFoundError(f"Encrypted file not found: {encrypted_path}")
            
            # Read encrypted file
            with open(enc_path, 'rb') as f:
                salt = f.read(32)
                iv = f.read(12)
                tag = f.read(16)
                ciphertext = f.read()
            
            # Derive key
            key = self.derive_key(password, salt)
            
            # Decrypt
            cipher = Cipher(algorithms.AES(key), modes.GCM(iv, tag), backend=self.backend)
            decryptor = cipher.decryptor()
            plaintext = decryptor.update(ciphertext) + decryptor.finalize()
            
            return plaintext
            
        except Exception as e:
            logger.error(f"Memory decryption failed: {e}")
            raise

def main():
    parser = argparse.ArgumentParser(description='Encrypt/Decrypt Telegram session files')
    parser.add_argument('action', choices=['encrypt', 'decrypt'], help='Action to perform')
    parser.add_argument('file', help='Session file path')
    parser.add_argument('-o', '--output', help='Output file path (decrypt only)')
    parser.add_argument('-p', '--password', help='Password (will prompt if not provided)')
    
    args = parser.parse_args()
    
    # Get password
    if args.password:
        password = args.password
    else:
        password = getpass.getpass("Enter encryption password: ")
    
    if not password:
        logger.error("Password is required")
        sys.exit(1)
    
    encryptor = SessionEncryption()
    
    if args.action == 'encrypt':
        success = encryptor.encrypt_file(args.file, password)
    else:  # decrypt
        success = encryptor.decrypt_file(args.file, password, args.output)
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
