#!/bin/bash

# Auto-Deploy Script for Telegram Bot to EC2
# Handles complete deployment process with your specific instance details

set -euo pipefail

# Configuration
readonly INSTANCE_IP="ec2-13-38-130-31.eu-west-3.compute.amazonaws.com"
readonly SSH_KEY="/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem"
readonly REMOTE_USER="ubuntu"
readonly REMOTE_DIR="/home/<USER>/telegram-bot"
readonly APP_DIR="/opt/telegram-bot"

# Colors
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m'

log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}Auto-Deploy Telegram Bot${NC}"
    echo -e "${BLUE}Target: ${INSTANCE_IP}${NC}"
    echo -e "${BLUE}================================${NC}"
    echo ""
}

# Validate prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check SSH key exists
    if [[ ! -f "$SSH_KEY" ]]; then
        error "SSH key not found: $SSH_KEY"
    fi
    
    # Fix SSH key permissions
    chmod 600 "$SSH_KEY"
    log "SSH key permissions verified"
    
    # Check required files
    local required_files=(
        "../lead_tracker.py"
        "../group_manager.py" 
        "../google_sheets.py"
        "../admin_commands.py"
        "../secrets_loader.py"
        "../encrypt_session.py"
        "../logging_config.py"
        "../requirements.txt"
        "../Logo.jpg"
        "../storage.json"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$file" ]]; then
            error "Required file not found: $file"
        fi
    done
    
    log "All required files found"
}

# Test SSH connection
test_connection() {
    log "Testing SSH connection..."
    
    if ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
       "$REMOTE_USER@$INSTANCE_IP" "echo 'Connection successful'" >/dev/null 2>&1; then
        log "SSH connection successful"
    else
        error "Cannot connect to $INSTANCE_IP. Check instance status and security groups."
    fi
}

# Create deployment package
create_package() {
    log "Creating deployment package..."
    
    # Create temporary package directory
    local package_dir="./package"
    rm -rf "$package_dir"
    mkdir -p "$package_dir"
    
    # Copy bot files
    local files_to_copy=(
        "lead_tracker.py"
        "group_manager.py"
        "google_sheets.py"
        "admin_commands.py"
        "secrets_loader.py"
        "encrypt_session.py"
        "logging_config.py"
        "requirements.txt"
        "Logo.jpg"
        "storage.json"
    )
    
    for file in "${files_to_copy[@]}"; do
        if [[ -f "../$file" ]]; then
            cp "../$file" "$package_dir/"
            log "✓ Copied $file"
        else
            warn "File not found: $file"
        fi
    done
    
    # Copy deployment scripts
    cp "./secure-deploy.sh" "$package_dir/"
    cp "./run_bot.py" "$package_dir/"
    
    # Copy credentials if they exist
    if [[ -f "../credentials.json" ]]; then
        cp "../credentials.json" "$package_dir/"
        chmod 600 "$package_dir/credentials.json"
        log "✓ Copied Google credentials"
    else
        warn "credentials.json not found - Google Sheets may not work"
    fi
    
    # Create .env file from secrets_loader if it doesn't exist
    if [[ ! -f "$package_dir/.env" ]] && [[ -f "../.env" ]]; then
        cp "../.env" "$package_dir/"
        chmod 600 "$package_dir/.env"
        log "✓ Copied environment file"
    fi
    
    # Set proper permissions
    chmod 755 "$package_dir"
    chmod 644 "$package_dir"/*.py 2>/dev/null || true
    chmod 755 "$package_dir"/*.sh 2>/dev/null || true
    
    log "Deployment package created"
}

# Upload files to EC2
upload_files() {
    log "Uploading files to EC2..."
    
    # Create remote directory
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$REMOTE_USER@$INSTANCE_IP" \
        "mkdir -p $REMOTE_DIR"
    
    # Upload with rsync for better handling
    if command -v rsync >/dev/null 2>&1; then
        rsync -avz --progress --delete \
            -e "ssh -i $SSH_KEY -o StrictHostKeyChecking=no" \
            ./package/ "$REMOTE_USER@$INSTANCE_IP:$REMOTE_DIR/"
    else
        # Fallback to scp
        scp -i "$SSH_KEY" -o StrictHostKeyChecking=no -r \
            ./package/* "$REMOTE_USER@$INSTANCE_IP:$REMOTE_DIR/"
    fi
    
    log "Files uploaded successfully"
}

# Deploy on remote server
deploy_remote() {
    log "Deploying on remote server..."
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$REMOTE_USER@$INSTANCE_IP" << 'EOF'
        cd /home/<USER>/telegram-bot
        
        echo "Setting permissions..."
        chmod +x secure-deploy.sh
        chmod 600 .env 2>/dev/null || echo "No .env file found"
        chmod 600 credentials.json 2>/dev/null || echo "No credentials.json found"
        
        echo "Running deployment script..."
        ./secure-deploy.sh
        
        echo "Starting bot service..."
        sudo systemctl start telegram-bot
        sudo systemctl enable telegram-bot
        
        echo "Checking service status..."
        sleep 5
        sudo systemctl status telegram-bot --no-pager
EOF
    
    log "Remote deployment completed"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no "$REMOTE_USER@$INSTANCE_IP" << 'EOF'
        echo "Service Status:"
        sudo systemctl is-active telegram-bot || echo "Service not running"
        
        echo ""
        echo "Recent Logs:"
        sudo journalctl -u telegram-bot --no-pager -n 10 || echo "No logs available"
        
        echo ""
        echo "File Permissions:"
        ls -la /opt/telegram-bot/.env 2>/dev/null || echo ".env not found"
        ls -la /opt/telegram-bot/credentials.json 2>/dev/null || echo "credentials.json not found"
EOF
    
    log "Deployment verification completed"
}

# Cleanup
cleanup() {
    log "Cleaning up temporary files..."
    rm -rf ./package
    log "Cleanup completed"
}

# Main function
main() {
    print_header
    
    # Set cleanup trap
    trap cleanup EXIT
    
    check_prerequisites
    test_connection
    create_package
    upload_files
    deploy_remote
    verify_deployment
    
    echo ""
    log "🎉 Deployment completed successfully!"
    echo ""
    log "Your bot is now running on: $INSTANCE_IP"
    log "To check status: ssh -i $SSH_KEY $REMOTE_USER@$INSTANCE_IP 'sudo systemctl status telegram-bot'"
    log "To view logs: ssh -i $SSH_KEY $REMOTE_USER@$INSTANCE_IP 'sudo journalctl -u telegram-bot -f'"
    echo ""
}

# Run if executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
