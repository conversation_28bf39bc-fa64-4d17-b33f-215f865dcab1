# Telegram Bot Auto-Deployment

This folder contains everything needed to automatically deploy your Telegram bot to your EC2 instance.

## Quick Start

1. **Prepare your environment file**:
   ```bash
   # Create .env file in the parent directory with your credentials
   cd ..
   nano .env
   ```

2. **Add your credentials to .env**:
   ```
   API_ID=your_api_id
   API_HASH=your_api_hash
   ADMIN_USER_ID=your_user_id
   ADMIN_CHAT_ID=your_chat_id
   CONTROL_BOT_TOKEN=your_bot_token
   GOOGLE_SHEET_NAME=your_sheet_name
   SESSION_ENCRYPTION_PASSWORD=your_encryption_password
   ```

3. **Run the quick deployment**:
   ```bash
   cd deployment
   chmod +x quick-deploy.sh
   ./quick-deploy.sh
   ```

## Files Included

### Deployment Scripts
- **`quick-deploy.sh`** - One-click deployment script (recommended)
- **`auto-deploy.sh`** - Full automated deployment with all checks
- **`secure-deploy.sh`** - Server-side deployment script
- **`run_bot.py`** - Production bot runner with error handling

### Configuration
- **`README.md`** - This file

## Deployment Process

The deployment automatically:

1. ✅ Validates all required files and credentials
2. 🔗 Tests SSH connection to your EC2 instance
3. 📦 Creates a secure deployment package
4. ⬆️ Uploads files to the server
5. 🛠️ Installs dependencies and configures the system
6. 🔒 Sets up security (firewall, permissions, etc.)
7. 🚀 Creates and starts the systemd service
8. ✅ Verifies the deployment

## Server Details

- **Instance**: `ec2-13-38-130-31.eu-west-3.compute.amazonaws.com`
- **SSH Key**: `/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem`
- **User**: `ubuntu`
- **App Directory**: `/opt/telegram-bot`
- **Service Name**: `telegram-bot`

## Post-Deployment Commands

### Check Bot Status
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo systemctl status telegram-bot'
```

### View Live Logs
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo journalctl -u telegram-bot -f'
```

### Restart Bot
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo systemctl restart telegram-bot'
```

### SSH to Server
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL>
```

## Troubleshooting

### Connection Issues
- Ensure your EC2 instance is running
- Check security group allows SSH (port 22) from your IP
- Verify SSH key path is correct

### Bot Not Starting
```bash
# Check service status
sudo systemctl status telegram-bot

# View detailed logs
sudo journalctl -u telegram-bot -n 50

# Check file permissions
ls -la /opt/telegram-bot/.env
ls -la /opt/telegram-bot/credentials.json
```

### Environment Issues
- Ensure `.env` file exists in parent directory
- Verify all required environment variables are set
- Check `credentials.json` for Google Sheets integration

## Security Features

The deployment includes:
- 🔒 Firewall configuration (UFW)
- 🛡️ Fail2ban for intrusion prevention
- 🔐 Secure file permissions (600 for sensitive files)
- 📝 Log rotation and management
- 🔄 Automatic security updates
- 👤 Non-root service execution

## Monitoring

The bot includes built-in monitoring:
- Health checks every 5 minutes
- Automatic restart on failure
- Resource usage monitoring
- Log rotation (7 days retention)

## File Structure on Server

```
/opt/telegram-bot/
├── lead_tracker.py          # Main bot file
├── group_manager.py         # Group management
├── google_sheets.py         # Sheets integration
├── admin_commands.py        # Admin commands
├── secrets_loader.py        # Secrets management
├── encrypt_session.py       # Session encryption
├── logging_config.py        # Logging setup
├── run_bot.py              # Production runner
├── .env                    # Environment variables (600)
├── credentials.json        # Google credentials (600)
├── requirements.txt        # Python dependencies
├── venv/                   # Python virtual environment
├── backups/               # Automated backups
└── logs/                  # Application logs
```

## Support

If you encounter issues:
1. Check the logs: `sudo journalctl -u telegram-bot -f`
2. Verify file permissions and environment variables
3. Ensure all required credentials are properly set
4. Check network connectivity and security groups

## Updates

To update the bot:
1. Make changes to your local files
2. Run the deployment script again
3. The script will update files and restart the service automatically
