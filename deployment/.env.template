# Telegram Bot Environment Configuration
# Copy this file to ../.env and fill in your actual values

# Telegram API Credentials (from https://my.telegram.org)
API_ID=your_api_id_here
API_HASH=your_api_hash_here

# Admin Configuration
ADMIN_USER_ID=your_telegram_user_id
ADMIN_CHAT_ID=your_admin_group_chat_id

# Bot Token (from @BotFather)
CONTROL_BOT_TOKEN=your_bot_token_here

# Google Sheets Configuration
GOOGLE_SHEET_NAME=your_google_sheet_name

# Security
SESSION_ENCRYPTION_PASSWORD=your_strong_encryption_password

# Optional: AWS Configuration (if using AWS features)
# AWS_ACCESS_KEY_ID=your_aws_access_key
# AWS_SECRET_ACCESS_KEY=your_aws_secret_key
# AWS_REGION=eu-west-3
