#!/usr/bin/env python3
"""
Bot Runner for Production Deployment
Handles proper startup, logging, and error recovery
"""

import sys
import os
import logging
import asyncio
import signal
from pathlib import Path

# Set up logging
log_dir = Path("/var/log/telegram-bot")
log_dir.mkdir(exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_dir / "bot.log"),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class BotRunner:
    def __init__(self):
        self.running = True
        self.bot_task = None
        
    def signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
        if self.bot_task:
            self.bot_task.cancel()
    
    async def run_bot(self):
        """Run the bot with error handling and restart capability"""
        try:
            # Change to app directory
            os.chdir("/opt/telegram-bot")
            
            # Import and run bot
            logger.info("Starting Telegram Lead Tracker Bot...")
            from lead_tracker import main as bot_main
            
            # Run the bot
            await bot_main()
            
        except asyncio.CancelledError:
            logger.info("Bot task cancelled")
            raise
        except Exception as e:
            logger.error(f"Bot crashed: {e}", exc_info=True)
            raise
    
    async def main(self):
        """Main runner with restart logic"""
        # Set up signal handlers
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)
        
        restart_count = 0
        max_restarts = 5
        
        while self.running and restart_count < max_restarts:
            try:
                logger.info(f"Starting bot (attempt {restart_count + 1})")
                self.bot_task = asyncio.create_task(self.run_bot())
                await self.bot_task
                
                # If we get here, bot exited normally
                logger.info("Bot exited normally")
                break
                
            except asyncio.CancelledError:
                logger.info("Bot cancelled, shutting down")
                break
            except Exception as e:
                restart_count += 1
                logger.error(f"Bot failed (attempt {restart_count}): {e}")
                
                if restart_count < max_restarts and self.running:
                    wait_time = min(30 * restart_count, 300)  # Max 5 minutes
                    logger.info(f"Restarting in {wait_time} seconds...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error("Max restart attempts reached or shutdown requested")
                    break
        
        logger.info("Bot runner exiting")

def main():
    """Entry point"""
    try:
        runner = BotRunner()
        asyncio.run(runner.main())
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
