# Complete Deployment Guide

## Prerequisites

1. **SSH Key**: Ensure you have the SSH key at `/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem`
2. **Environment File**: Create a `.env` file in the parent directory with your bot credentials
3. **Google Credentials**: (Optional) Place `credentials.json` in the parent directory for Google Sheets

## Step-by-Step Deployment

### Option 1: Quick Deployment (Recommended)

```bash
cd deployment
./quick-deploy.sh
```

This single command will:
- Check all prerequisites
- Test connection to your EC2 instance
- Deploy and configure everything automatically
- Start the bot service

### Option 2: Manual Deployment

If you prefer more control:

```bash
cd deployment
./auto-deploy.sh
```

### Option 3: Step-by-Step Manual Process

1. **Test Connection**:
   ```bash
   ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL>
   ```

2. **Upload Files**:
   ```bash
   # From deployment directory
   rsync -avz --progress -e "ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem" \
     ../lead_tracker.py ../group_manager.py ../google_sheets.py ../admin_commands.py \
     ../secrets_loader.py ../encrypt_session.py ../logging_config.py \
     ../requirements.txt ../Logo.jpg ../storage.json ../.env ../credentials.json \
     ./secure-deploy.sh ./run_bot.py \
     <EMAIL>:/home/<USER>/telegram-bot/
   ```

3. **Deploy on Server**:
   ```bash
   ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL>
   cd /home/<USER>/telegram-bot
   chmod +x secure-deploy.sh
   ./secure-deploy.sh
   sudo systemctl start telegram-bot
   ```

## Post-Deployment

### Check Status
```bash
./check-status.sh
```

### Monitor Logs
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo journalctl -u telegram-bot -f'
```

### Restart Bot
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo systemctl restart telegram-bot'
```

## Environment Variables Required

Create a `.env` file in the parent directory with:

```
API_ID=your_api_id
API_HASH=your_api_hash
ADMIN_USER_ID=your_user_id
ADMIN_CHAT_ID=your_chat_id
CONTROL_BOT_TOKEN=your_bot_token
GOOGLE_SHEET_NAME=your_sheet_name
SESSION_ENCRYPTION_PASSWORD=your_encryption_password
```

## Troubleshooting

### Common Issues

1. **Connection Refused**:
   - Check if EC2 instance is running
   - Verify security group allows SSH (port 22)
   - Ensure SSH key permissions are 600

2. **Bot Won't Start**:
   - Check logs: `sudo journalctl -u telegram-bot -n 50`
   - Verify environment variables in `/opt/telegram-bot/.env`
   - Check file permissions

3. **Google Sheets Not Working**:
   - Ensure `credentials.json` is present and has correct permissions (600)
   - Verify service account has access to the sheet

### Log Locations

- **Service Logs**: `sudo journalctl -u telegram-bot`
- **Application Logs**: `/var/log/telegram-bot/bot.log`
- **Health Logs**: `/var/log/telegram-bot/health.log`

## Security Features

The deployment includes:
- Firewall configuration (UFW)
- Fail2ban for intrusion prevention
- Secure file permissions
- Non-root service execution
- Automatic security updates
- Log rotation

## File Structure

```
deployment/
├── auto-deploy.sh          # Full automated deployment
├── quick-deploy.sh         # One-click deployment
├── check-status.sh         # Status checker
├── secure-deploy.sh        # Server-side deployment
├── run_bot.py             # Production bot runner
├── .env.template          # Environment template
├── README.md              # Basic instructions
└── DEPLOYMENT_GUIDE.md    # This comprehensive guide
```

## Support

If you encounter issues:
1. Run `./check-status.sh` to diagnose problems
2. Check the logs for error messages
3. Verify all credentials and environment variables
4. Ensure network connectivity and security groups are correct
