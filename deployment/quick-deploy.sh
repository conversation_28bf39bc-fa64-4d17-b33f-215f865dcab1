#!/bin/bash

# Quick Deploy Script - Simplified deployment for your specific instance
# This script handles the complete deployment process in one go

set -euo pipefail

# Configuration - Update these if needed
INSTANCE_IP="ec2-13-38-130-31.eu-west-3.compute.amazonaws.com"
SSH_KEY="/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
    exit 1
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════╗"
    echo "║        Quick Deploy Script           ║"
    echo "║     Telegram Bot Deployment          ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# Check if we're in the right directory
check_directory() {
    if [[ ! -f "auto-deploy.sh" ]]; then
        error "Please run this script from the deployment directory"
    fi
    
    if [[ ! -f "$SSH_KEY" ]]; then
        error "SSH key not found: $SSH_KEY"
    fi
    
    log "Directory check passed"
}

# Check environment setup
check_environment() {
    log "Checking environment setup..."
    
    # Check if .env exists in parent directory
    if [[ ! -f "../.env" ]]; then
        warn "No .env file found in parent directory"
        echo ""
        echo "You need to create a .env file with your bot credentials."
        echo "Example .env file content:"
        echo ""
        echo "API_ID=your_api_id"
        echo "API_HASH=your_api_hash"
        echo "ADMIN_USER_ID=your_user_id"
        echo "ADMIN_CHAT_ID=your_chat_id"
        echo "CONTROL_BOT_TOKEN=your_bot_token"
        echo "GOOGLE_SHEET_NAME=your_sheet_name"
        echo "SESSION_ENCRYPTION_PASSWORD=your_encryption_password"
        echo ""
        read -p "Press Enter after creating the .env file..."
        
        if [[ ! -f "../.env" ]]; then
            error ".env file still not found"
        fi
    fi
    
    # Check credentials.json
    if [[ ! -f "../credentials.json" ]]; then
        warn "No credentials.json found - Google Sheets integration may not work"
        echo "Download your Google Service Account JSON file and save it as credentials.json"
        read -p "Press Enter to continue without Google Sheets or Ctrl+C to abort..."
    fi
    
    log "Environment check completed"
}

# Test connection
test_connection() {
    log "Testing connection to $INSTANCE_IP..."
    
    if ssh -i "$SSH_KEY" -o ConnectTimeout=10 -o StrictHostKeyChecking=no \
       ubuntu@"$INSTANCE_IP" "echo 'Connection test successful'" >/dev/null 2>&1; then
        log "✅ Connection successful"
    else
        error "❌ Cannot connect to instance. Check:"
        echo "  - Instance is running"
        echo "  - Security group allows SSH from your IP"
        echo "  - SSH key path is correct"
    fi
}

# Run deployment
run_deployment() {
    log "Starting deployment process..."
    
    chmod +x auto-deploy.sh
    
    if ./auto-deploy.sh; then
        log "✅ Deployment completed successfully!"
    else
        error "❌ Deployment failed"
    fi
}

# Post-deployment checks
post_deployment_check() {
    log "Running post-deployment checks..."
    
    echo ""
    echo "Checking bot status on remote server..."
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no ubuntu@"$INSTANCE_IP" << 'EOF'
        echo "🔍 Service Status:"
        sudo systemctl status telegram-bot --no-pager -l || echo "Service not found"
        
        echo ""
        echo "📋 Recent Logs (last 5 lines):"
        sudo journalctl -u telegram-bot --no-pager -n 5 || echo "No logs available"
        
        echo ""
        echo "💾 Memory Usage:"
        free -h | head -2
        
        echo ""
        echo "💿 Disk Usage:"
        df -h / | tail -1
EOF
    
    echo ""
    log "Post-deployment check completed"
}

# Show connection info
show_connection_info() {
    echo ""
    echo -e "${BLUE}═══════════════════════════════════════${NC}"
    echo -e "${BLUE}         Deployment Complete!          ${NC}"
    echo -e "${BLUE}═══════════════════════════════════════${NC}"
    echo ""
    echo "Your bot is now running on: $INSTANCE_IP"
    echo ""
    echo "Useful commands:"
    echo ""
    echo "📊 Check bot status:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo systemctl status telegram-bot'"
    echo ""
    echo "📝 View live logs:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo journalctl -u telegram-bot -f'"
    echo ""
    echo "🔄 Restart bot:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo systemctl restart telegram-bot'"
    echo ""
    echo "🛑 Stop bot:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo systemctl stop telegram-bot'"
    echo ""
    echo "🖥️  SSH to server:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP"
    echo ""
}

# Main function
main() {
    print_banner
    
    log "Starting quick deployment process..."
    echo ""
    
    check_directory
    check_environment
    test_connection
    
    echo ""
    log "All checks passed! Starting deployment..."
    echo ""
    
    run_deployment
    post_deployment_check
    show_connection_info
    
    echo ""
    log "🎉 Quick deployment completed successfully!"
}

# Run main function
main "$@"
