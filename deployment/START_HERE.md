# 🚀 START HERE - Telegram Bot Deployment

## What You Have

A complete deployment package for your Telegram bot with:
- **Target Server**: `ec2-13-38-130-31.eu-west-3.compute.amazonaws.com`
- **SSH Key**: `/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem`
- **Automated deployment scripts**
- **Security hardening**
- **Monitoring and logging**

## Quick Start (2 Steps)

### Step 1: Create Environment File

```bash
# Go to parent directory
cd ..

# Copy the template
cp deployment/.env.template .env

# Edit with your credentials
nano .env
```

Add your actual values:
```
API_ID=123456789
API_HASH=abcdef1234567890abcdef1234567890
ADMIN_USER_ID=987654321
ADMIN_CHAT_ID=-1001234567890
CONTROL_BOT_TOKEN=1234567890:ABCdefGHIjklMNOpqrsTUVwxyz
GOOGLE_SHEET_NAME=Lead Tracker
SESSION_ENCRYPTION_PASSWORD=your_strong_password_here
```

### Step 2: Deploy

```bash
# Go to deployment folder
cd deployment

# Run one-click deployment
./quick-deploy.sh
```

That's it! Your bot will be deployed and running.

## What Happens During Deployment

1. ✅ **Validates** all files and credentials
2. 🔗 **Tests** SSH connection to EC2
3. 📦 **Packages** all bot files securely
4. ⬆️ **Uploads** to server
5. 🛠️ **Installs** Python dependencies
6. 🔒 **Configures** security (firewall, permissions)
7. 🚀 **Creates** systemd service
8. ▶️ **Starts** the bot

## After Deployment

### Check Status
```bash
./check-status.sh
```

### View Live Logs
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo journalctl -u telegram-bot -f'
```

### Restart Bot
```bash
ssh -i /Users/<USER>/Downloads/telegramleadtrackerkeypair.pem <EMAIL> 'sudo systemctl restart telegram-bot'
```

## Files in This Package

- **`quick-deploy.sh`** - One-click deployment (recommended)
- **`auto-deploy.sh`** - Full automated deployment
- **`check-status.sh`** - Check bot status and health
- **`secure-deploy.sh`** - Server-side deployment script
- **`run_bot.py`** - Production bot runner
- **`.env.template`** - Environment variables template
- **`README.md`** - Detailed instructions
- **`DEPLOYMENT_GUIDE.md`** - Complete deployment guide

## Need Help?

1. **Read the logs**: `./check-status.sh`
2. **Check the guides**: `README.md` and `DEPLOYMENT_GUIDE.md`
3. **Verify credentials**: Ensure `.env` file has correct values
4. **Test connection**: Make sure you can SSH to the server

## Security Features Included

- 🔒 Firewall (UFW) configured
- 🛡️ Fail2ban for intrusion prevention
- 🔐 Secure file permissions (600 for sensitive files)
- 📝 Log rotation and management
- 🔄 Automatic security updates
- 👤 Non-root service execution

Your bot will be production-ready with enterprise-level security!
