#!/bin/bash

# Bot Status Checker
# Quick script to check the status of your deployed bot

set -euo pipefail

# Configuration
INSTANCE_IP="ec2-13-38-130-31.eu-west-3.compute.amazonaws.com"
SSH_KEY="/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_header() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════╗"
    echo "║         Bot Status Checker           ║"
    echo "║    $INSTANCE_IP    ║"
    echo "╚══════════════════════════════════════╝"
    echo -e "${NC}"
    echo ""
}

# Test connection
test_connection() {
    log "Testing connection..."
    
    if ssh -i "$SSH_KEY" -o ConnectTimeout=5 -o StrictHostKeyChecking=no \
       ubuntu@"$INSTANCE_IP" "echo 'Connected'" >/dev/null 2>&1; then
        log "✅ Connection successful"
        return 0
    else
        error "❌ Cannot connect to instance"
        return 1
    fi
}

# Get comprehensive status
get_status() {
    log "Fetching bot status..."
    echo ""
    
    ssh -i "$SSH_KEY" -o StrictHostKeyChecking=no ubuntu@"$INSTANCE_IP" << 'EOF'
        echo "🤖 Bot Service Status:"
        echo "======================"
        if systemctl is-active --quiet telegram-bot; then
            echo "✅ Service is RUNNING"
        else
            echo "❌ Service is NOT RUNNING"
        fi
        
        echo ""
        echo "📊 Service Details:"
        sudo systemctl status telegram-bot --no-pager -l | head -15
        
        echo ""
        echo "📝 Recent Logs (last 10 lines):"
        echo "================================"
        sudo journalctl -u telegram-bot --no-pager -n 10 | tail -10
        
        echo ""
        echo "💾 System Resources:"
        echo "==================="
        echo "Memory Usage:"
        free -h | grep -E "Mem|Swap"
        
        echo ""
        echo "Disk Usage:"
        df -h / | tail -1
        
        echo ""
        echo "🔒 Security Status:"
        echo "=================="
        echo "Firewall: $(sudo ufw status | head -1)"
        echo "Fail2ban: $(sudo systemctl is-active fail2ban 2>/dev/null || echo 'not installed')"
        
        echo ""
        echo "📁 File Permissions:"
        echo "==================="
        if [ -f "/opt/telegram-bot/.env" ]; then
            echo ".env: $(stat -c "%a" /opt/telegram-bot/.env)"
        else
            echo ".env: not found"
        fi
        
        if [ -f "/opt/telegram-bot/credentials.json" ]; then
            echo "credentials.json: $(stat -c "%a" /opt/telegram-bot/credentials.json)"
        else
            echo "credentials.json: not found"
        fi
        
        echo ""
        echo "⏰ System Uptime:"
        echo "================"
        uptime
        
        echo ""
        echo "🔄 Last Service Restart:"
        echo "======================="
        sudo systemctl show telegram-bot --property=ActiveEnterTimestamp --no-pager
EOF
}

# Show quick commands
show_commands() {
    echo ""
    echo -e "${BLUE}Quick Commands:${NC}"
    echo "=============="
    echo ""
    echo "🔄 Restart bot:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo systemctl restart telegram-bot'"
    echo ""
    echo "🛑 Stop bot:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo systemctl stop telegram-bot'"
    echo ""
    echo "▶️  Start bot:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo systemctl start telegram-bot'"
    echo ""
    echo "📝 Live logs:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP 'sudo journalctl -u telegram-bot -f'"
    echo ""
    echo "🖥️  SSH to server:"
    echo "   ssh -i $SSH_KEY ubuntu@$INSTANCE_IP"
    echo ""
}

# Main function
main() {
    print_header
    
    if test_connection; then
        get_status
        show_commands
    else
        echo ""
        error "Cannot connect to the server. Please check:"
        echo "  - Instance is running"
        echo "  - Security group allows SSH from your IP"
        echo "  - SSH key path is correct: $SSH_KEY"
    fi
    
    echo ""
}

# Run main function
main "$@"
