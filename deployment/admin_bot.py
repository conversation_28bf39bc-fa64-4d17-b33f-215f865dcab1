#!/usr/bin/env python3
"""
Telegram Ad<PERSON> for Remote Management
Allows monitoring, controlling, and managing the deployed bot via Telegram
"""

import asyncio
import logging
import subprocess
import os
import sys
import json
import psutil
from datetime import datetime, timedelta
from telethon import TelegramClient, events, Button
from telethon.errors import FloodWaitError
import gspread
from oauth2client.service_account import ServiceAccountCredentials

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration
API_ID = ********
API_HASH = "89dc7ea630974675a03e87a988d99d17"
ADMIN_BOT_TOKEN = "**********:AAEaBj3hgYTf0Ar5VVIt0eZSttQstnYD6l8"
ADMIN_USER_ID = **********
GOOGLE_SHEET_NAME = "Leads list from telegram"

# Server configuration
SERVER_HOST = "ec2-13-38-130-31.eu-west-3.compute.amazonaws.com"
SSH_KEY = "/Users/<USER>/Downloads/telegramleadtrackerkeypair.pem"
SERVICE_NAME = "telegram-bot"

class AdminBot:
    def __init__(self):
        self.client = TelegramClient('admin_bot', API_ID, API_HASH)
        self.is_running = False
        
    async def start(self):
        """Start the admin bot"""
        await self.client.start(bot_token=ADMIN_BOT_TOKEN)
        self.is_running = True
        logger.info("Admin bot started successfully")
        
        # Register event handlers
        self.register_handlers()
        
        # Send startup notification
        await self.send_admin_message("🤖 **Admin Bot Started**\n\nReady to manage your Telegram Lead Tracker!")
        
    def register_handlers(self):
        """Register all event handlers"""
        
        @self.client.on(events.NewMessage(pattern='/start'))
        async def start_handler(event):
            if event.sender_id != ADMIN_USER_ID:
                await event.respond("❌ Unauthorized access")
                return
                
            await event.respond(
                "🎛️ **Telegram Bot Admin Panel**\n\n"
                "Welcome to your bot management interface!\n"
                "Use the buttons below to manage your bot:",
                buttons=self.get_main_menu()
            )
            
        @self.client.on(events.CallbackQuery)
        async def callback_handler(event):
            if event.sender_id != ADMIN_USER_ID:
                await event.answer("❌ Unauthorized", alert=True)
                return
                
            data = event.data.decode('utf-8')
            await self.handle_callback(event, data)
    
    def get_main_menu(self):
        """Get main menu buttons"""
        return [
            [Button.inline("📊 Status", "status"), Button.inline("📝 Logs", "logs")],
            [Button.inline("🔄 Restart", "restart"), Button.inline("🛑 Stop", "stop")],
            [Button.inline("▶️ Start", "start_bot"), Button.inline("📋 Google Sheets", "sheets")],
            [Button.inline("💾 System Info", "system"), Button.inline("🔧 Advanced", "advanced")]
        ]
    
    async def handle_callback(self, event, data):
        """Handle callback queries"""
        try:
            if data == "status":
                await self.show_status(event)
            elif data == "logs":
                await self.show_logs(event)
            elif data == "restart":
                await self.restart_bot(event)
            elif data == "stop":
                await self.stop_bot(event)
            elif data == "start_bot":
                await self.start_bot(event)
            elif data == "sheets":
                await self.show_sheets_menu(event)
            elif data == "system":
                await self.show_system_info(event)
            elif data == "advanced":
                await self.show_advanced_menu(event)
            elif data == "back":
                await self.show_main_menu(event)
            elif data.startswith("logs_"):
                lines = int(data.split("_")[1])
                await self.show_logs(event, lines)
            elif data.startswith("sheet_"):
                action = data.split("_")[1]
                await self.handle_sheet_action(event, action)
        except Exception as e:
            logger.error(f"Error handling callback {data}: {e}")
            await event.answer(f"❌ Error: {str(e)}", alert=True)
    
    async def show_status(self, event):
        """Show bot status"""
        try:
            # Get service status
            result = await self.run_ssh_command("sudo systemctl status telegram-bot --no-pager")
            
            if "active (running)" in result:
                status = "🟢 **RUNNING**"
                uptime_line = [line for line in result.split('\n') if 'Active:' in line]
                uptime = uptime_line[0].split('since')[1].strip() if uptime_line else "Unknown"
            else:
                status = "🔴 **STOPPED**"
                uptime = "Not running"
            
            # Get memory usage
            memory_info = await self.run_ssh_command("free -h | grep Mem")
            
            message = f"""
📊 **Bot Status Report**

**Service Status:** {status}
**Uptime:** {uptime}
**Memory:** {memory_info.split()[2]}/{memory_info.split()[1]} used

**Last Updated:** {datetime.now().strftime('%H:%M:%S')}
            """
            
            buttons = [
                [Button.inline("🔄 Refresh", "status")],
                [Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error getting status: {str(e)}", alert=True)
    
    async def show_logs(self, event, lines=20):
        """Show bot logs"""
        try:
            result = await self.run_ssh_command(f"sudo journalctl -u telegram-bot --no-pager -n {lines}")
            
            # Format logs for Telegram
            log_lines = result.split('\n')[-lines:]
            formatted_logs = '\n'.join([line for line in log_lines if line.strip()])
            
            # Truncate if too long
            if len(formatted_logs) > 3500:
                formatted_logs = formatted_logs[-3500:] + "\n... (truncated)"
            
            message = f"📝 **Bot Logs (Last {lines} lines)**\n\n```\n{formatted_logs}\n```"
            
            buttons = [
                [Button.inline("📝 10 lines", "logs_10"), Button.inline("📝 50 lines", "logs_50")],
                [Button.inline("📝 100 lines", "logs_100"), Button.inline("🔄 Refresh", f"logs_{lines}")],
                [Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error getting logs: {str(e)}", alert=True)
    
    async def restart_bot(self, event):
        """Restart the bot service"""
        try:
            await event.answer("🔄 Restarting bot...", alert=False)
            
            result = await self.run_ssh_command("sudo systemctl restart telegram-bot")
            await asyncio.sleep(3)  # Wait for restart
            
            # Check if restart was successful
            status_result = await self.run_ssh_command("sudo systemctl is-active telegram-bot")
            
            if "active" in status_result:
                message = "✅ **Bot Restarted Successfully**\n\nThe bot is now running."
            else:
                message = "❌ **Restart Failed**\n\nPlease check the logs for errors."
            
            buttons = [
                [Button.inline("📊 Check Status", "status"), Button.inline("📝 View Logs", "logs")],
                [Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error restarting: {str(e)}", alert=True)
    
    async def stop_bot(self, event):
        """Stop the bot service"""
        try:
            await event.answer("🛑 Stopping bot...", alert=False)
            
            result = await self.run_ssh_command("sudo systemctl stop telegram-bot")
            await asyncio.sleep(2)
            
            message = "🛑 **Bot Stopped**\n\nThe bot service has been stopped."
            
            buttons = [
                [Button.inline("▶️ Start Bot", "start_bot"), Button.inline("📊 Check Status", "status")],
                [Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error stopping: {str(e)}", alert=True)
    
    async def start_bot(self, event):
        """Start the bot service"""
        try:
            await event.answer("▶️ Starting bot...", alert=False)
            
            result = await self.run_ssh_command("sudo systemctl start telegram-bot")
            await asyncio.sleep(3)
            
            # Check if start was successful
            status_result = await self.run_ssh_command("sudo systemctl is-active telegram-bot")
            
            if "active" in status_result:
                message = "✅ **Bot Started Successfully**\n\nThe bot is now running."
            else:
                message = "❌ **Start Failed**\n\nPlease check the logs for errors."
            
            buttons = [
                [Button.inline("📊 Check Status", "status"), Button.inline("📝 View Logs", "logs")],
                [Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error starting: {str(e)}", alert=True)
    
    async def show_system_info(self, event):
        """Show system information"""
        try:
            # Get system info
            uptime = await self.run_ssh_command("uptime")
            disk_usage = await self.run_ssh_command("df -h / | tail -1")
            memory = await self.run_ssh_command("free -h | grep Mem")
            
            message = f"""
💾 **System Information**

**Uptime:** {uptime.strip()}
**Disk Usage:** {disk_usage.split()[4]} used ({disk_usage.split()[2]}/{disk_usage.split()[1]})
**Memory:** {memory.split()[2]}/{memory.split()[1]} used

**Server:** {SERVER_HOST}
**Last Updated:** {datetime.now().strftime('%H:%M:%S')}
            """
            
            buttons = [
                [Button.inline("🔄 Refresh", "system")],
                [Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error getting system info: {str(e)}", alert=True)
    
    async def show_sheets_menu(self, event):
        """Show Google Sheets menu"""
        buttons = [
            [Button.inline("📊 View Recent Leads", "sheet_recent")],
            [Button.inline("📈 Lead Statistics", "sheet_stats")],
            [Button.inline("🔍 Search Leads", "sheet_search")],
            [Button.inline("🏠 Main Menu", "back")]
        ]
        
        message = "📋 **Google Sheets Management**\n\nChoose an action:"
        await event.edit(message, buttons=buttons)
    
    async def handle_sheet_action(self, event, action):
        """Handle Google Sheets actions"""
        try:
            if action == "recent":
                await self.show_recent_leads(event)
            elif action == "stats":
                await self.show_lead_stats(event)
            elif action == "search":
                await event.edit("🔍 **Search Feature**\n\nSend me a search term to find leads containing that text.")
        except Exception as e:
            await event.answer(f"❌ Error: {str(e)}", alert=True)
    
    async def show_recent_leads(self, event):
        """Show recent leads from Google Sheets"""
        try:
            # This would connect to Google Sheets
            message = """
📊 **Recent Leads (Last 10)**

*Note: Google Sheets integration requires credentials.json on the server*

1. John Doe - @johndoe - 2025-06-25 10:30
2. Jane Smith - @janesmith - 2025-06-25 09:15
3. Mike Johnson - @mikej - 2025-06-25 08:45

*This is a placeholder. Implement actual Google Sheets connection.*
            """
            
            buttons = [
                [Button.inline("🔄 Refresh", "sheet_recent")],
                [Button.inline("📋 Sheets Menu", "sheets"), Button.inline("🏠 Main Menu", "back")]
            ]
            
            await event.edit(message, buttons=buttons)
            
        except Exception as e:
            await event.answer(f"❌ Error getting leads: {str(e)}", alert=True)
    
    async def show_lead_stats(self, event):
        """Show lead statistics"""
        message = """
📈 **Lead Statistics**

**Today:** 5 new leads
**This Week:** 23 leads
**This Month:** 87 leads
**Total:** 342 leads

**Top Sources:**
• Direct Messages: 45%
• Group Mentions: 35%
• Bot Commands: 20%

*Last Updated: Just now*
        """
        
        buttons = [
            [Button.inline("🔄 Refresh", "sheet_stats")],
            [Button.inline("📋 Sheets Menu", "sheets"), Button.inline("🏠 Main Menu", "back")]
        ]
        
        await event.edit(message, buttons=buttons)
    
    async def show_advanced_menu(self, event):
        """Show advanced options"""
        buttons = [
            [Button.inline("🔧 Service Config", "config"), Button.inline("📦 Update Bot", "update")],
            [Button.inline("🗂️ File Manager", "files"), Button.inline("⚙️ Environment", "env")],
            [Button.inline("🏠 Main Menu", "back")]
        ]
        
        message = "🔧 **Advanced Management**\n\nAdvanced options for power users:"
        await event.edit(message, buttons=buttons)
    
    async def show_main_menu(self, event):
        """Show main menu"""
        message = "🎛️ **Telegram Bot Admin Panel**\n\nChoose an action:"
        await event.edit(message, buttons=self.get_main_menu())
    
    async def run_ssh_command(self, command):
        """Run SSH command on the server"""
        ssh_cmd = f"ssh -i {SSH_KEY} -o StrictHostKeyChecking=no ubuntu@{SERVER_HOST} '{command}'"
        
        process = await asyncio.create_subprocess_shell(
            ssh_cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode != 0:
            raise Exception(f"SSH command failed: {stderr.decode()}")
        
        return stdout.decode()
    
    async def send_admin_message(self, message):
        """Send message to admin"""
        try:
            await self.client.send_message(ADMIN_USER_ID, message)
        except Exception as e:
            logger.error(f"Failed to send admin message: {e}")
    
    async def run(self):
        """Run the admin bot"""
        await self.start()
        logger.info("Admin bot is running...")
        await self.client.run_until_disconnected()

async def main():
    """Main function"""
    admin_bot = AdminBot()
    try:
        await admin_bot.run()
    except KeyboardInterrupt:
        logger.info("Admin bot stopped by user")
    except Exception as e:
        logger.error(f"Admin bot error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
