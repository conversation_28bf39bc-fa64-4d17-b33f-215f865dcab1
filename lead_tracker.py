import os
import async<PERSON>
import json
import logging
import tempfile
import time
from pathlib import Path
from datetime import datetime, timezone
from telethon import Telegram<PERSON>lient, events, Button
from telethon.errors import FloodWaitError, SessionPasswordNeededError
from telethon.tl.functions.users import GetFullUserRequest
from google_sheets import log_lead, update_lead_status, append_tag_to_lead
from secrets_loader import get_all_secrets
from encrypt_session import SessionEncryption

# Configure structured logging
from logging_config import init_logging, log_user_action, log_admin_action, log_security_event

# Initialize logging system
init_logging(console=True, debug=False)
logger = logging.getLogger(__name__)

# Load secrets
try:
    secrets = get_all_secrets()
    API_ID = int(secrets["API_ID"])
    API_HASH = secrets["API_HASH"]
    ADMIN_CHAT_ID = int(secrets["ADMIN_CHAT_ID"])
    CONTROL_BOT_TOKEN = secrets["CONTROL_BOT_TOKEN"]
    SESSION_ENCRYPTION_PASSWORD = secrets.get("SESSION_ENCRYPTION_PASSWORD")
except Exception as e:
    logger.error(f"Failed to load secrets: {e}")
    raise

SESSION_NAME = "session"
BOT_SESSION_NAME = "bot_session"

# Session encryption handler
session_encryptor = SessionEncryption()

def get_decrypted_session_path(session_name: str) -> str:
    """Get decrypted session file path, decrypting if necessary"""
    encrypted_path = f"{session_name}.session.enc"
    session_path = f"{session_name}.session"

    # If encrypted session exists and no plain session, decrypt to temp location
    if os.path.exists(encrypted_path) and not os.path.exists(session_path):
        if not SESSION_ENCRYPTION_PASSWORD:
            logger.error("Encrypted session found but no decryption password available")
            raise ValueError("SESSION_ENCRYPTION_PASSWORD required for encrypted sessions")

        try:
            # Decrypt to memory and write to temporary session file
            decrypted_data = session_encryptor.decrypt_to_memory(encrypted_path, SESSION_ENCRYPTION_PASSWORD)
            with open(session_path, 'wb') as f:
                f.write(decrypted_data)
            os.chmod(session_path, 0o600)
            logger.info(f"Session decrypted successfully: {session_name}")
        except Exception as e:
            logger.error(f"Failed to decrypt session {session_name}: {e}")
            raise

    return session_path

# Initialize clients with encrypted session support
try:
    user_session_path = get_decrypted_session_path(SESSION_NAME)
    bot_session_path = get_decrypted_session_path(BOT_SESSION_NAME)

    client = TelegramClient(user_session_path, API_ID, API_HASH)
    bot_client = TelegramClient(bot_session_path, API_ID, API_HASH)

    logger.info("Telegram clients initialized with encrypted session support")
except Exception as e:
    logger.error(f"Failed to initialize Telegram clients: {e}")
    raise

STORAGE_PATH = "storage.json"
if os.path.exists(STORAGE_PATH):
    with open(STORAGE_PATH, "r") as f:
        storage_data = json.load(f)
        handled_users = storage_data.get("handled_user_ids", [])
        initiated_users = storage_data.get("initiated_user_ids", [])
else:
    handled_users = []
    initiated_users = []

def save_storage():
    with open(STORAGE_PATH, "w") as f:
        json.dump({
            "handled_user_ids": handled_users,
            "initiated_user_ids": initiated_users
        }, f)

def infer_tags(text):
    tags = []
    lowered = text.lower()
    if "audit" in lowered:
        tags.append("Audit")
    if "bug" in lowered or "bounty" in lowered:
        tags.append("Bug Bounty")
    if "monitoring" in lowered:
        tags.append("Monitoring")
    if "pen test" in lowered:
        tags.append("Pen Test")
    return tags

def infer_company(text, sender):
    """Extract company name from bio text with improved logic"""
    if not text:
        return ""

    text = text.strip()
    text_lower = text.lower()

    # Common company indicators
    company_indicators = [
        "ceo", "founder", "co-founder", "cto", "cfo", "coo", "vp", "director",
        "head of", "lead", "manager", "engineer", "developer", "analyst",
        "at ", " at ", "@", "works at", "working at", "employed at",
        "inc", "llc", "ltd", "corp", "corporation", "company", "startup",
        "tech", "technologies", "solutions", "systems", "labs", "studio"
    ]

    # Look for patterns like "CEO at CompanyName" or "CompanyName CEO"
    for indicator in company_indicators:
        if indicator in text_lower:
            # Split by the indicator and look for company name
            parts = text_lower.split(indicator)
            for part in parts:
                part = part.strip(" .,!@#$%^&*()[]{}|\\:;\"'<>?/")
                if part and len(part.split()) <= 3 and len(part) > 2:
                    # Capitalize properly
                    company = ' '.join(word.capitalize() for word in part.split())
                    if company and company.lower() not in ['the', 'and', 'or', 'of', 'in', 'on', 'at']:
                        return company

    # Look for capitalized words that might be company names
    words = text.split()
    for i, word in enumerate(words):
        if word[0].isupper() and len(word) > 2:
            # Check if it's followed by another capitalized word (compound company name)
            if i + 1 < len(words) and words[i + 1][0].isupper():
                return f"{word} {words[i + 1]}"
            elif word.lower() not in ['i', 'am', 'the', 'and', 'or', 'of', 'in', 'on', 'at']:
                return word

    # Fallback: if no company found in bio, use first name as per user preference
    if sender.first_name:
        return sender.first_name

    return ""

async def telegram_api_call_with_retry(func, *args, max_retries=3, **kwargs):
    """Wrapper for Telegram API calls with exponential backoff on rate limits"""
    for attempt in range(max_retries):
        try:
            return await func(*args, **kwargs)
        except FloodWaitError as e:
            wait_time = e.seconds
            logger.warning(
                f"Rate limit hit, waiting {wait_time}s (attempt {attempt + 1}/{max_retries})",
                extra={
                    'action': 'rate_limit_hit',
                    'wait_time': wait_time,
                    'attempt': attempt + 1,
                    'function': func.__name__
                }
            )
            if attempt < max_retries - 1:
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"Max retries exceeded for {func.__name__}")
                raise
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(
                    f"API call failed, retrying in {wait_time}s: {e}",
                    extra={
                        'action': 'api_call_retry',
                        'wait_time': wait_time,
                        'attempt': attempt + 1,
                        'error': str(e)
                    }
                )
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"API call failed after {max_retries} attempts: {e}")
                raise

async def send_admin_buttons(user_id, username, full_name):
    logger.info(
        f"Sending admin buttons for user: {user_id}",
        extra={
            'action': 'send_admin_buttons',
            'user_id': user_id,
            'username': username,
            'full_name': full_name
        }
    )

    # Ensure bot client is started
    if not bot_client.is_connected():
        await bot_client.start(bot_token=CONTROL_BOT_TOKEN)

    buttons = [
        [
            Button.inline("Tag: Audit", data=f"tag_audit:{user_id}"),
            Button.inline("Tag: Pen Test", data=f"tag_pen_test:{user_id}"),
            Button.inline("Tag: Bug Bounty", data=f"tag_bug:{user_id}"),
            Button.inline("Tag: Monitoring", data=f"tag_monitoring:{user_id}")
        ],
        [
            Button.inline("Cold → Warm", data=f"status_warm:{user_id}"),
            Button.inline("Create Group", data=f"create_group:{user_id}"),
            Button.inline("Export Lead", data=f"export:{user_id}")
        ]
    ]

    try:
        # Use bot client to send interactive buttons to admin chat with retry logic
        await telegram_api_call_with_retry(
            bot_client.send_message,
            ADMIN_CHAT_ID,
            f"🔔 Lead logged: {full_name} (@{username})",
            buttons=buttons
        )
        logger.info(
            f"✅ Admin buttons sent successfully to {ADMIN_CHAT_ID} via bot",
            extra={
                'action': 'admin_buttons_sent',
                'admin_chat_id': ADMIN_CHAT_ID,
                'user_id': user_id,
                'username': username
            }
        )
    except Exception as e:
        logger.error(
            f"Failed to send admin buttons via bot: {e}",
            extra={
                'action': 'admin_buttons_failed',
                'admin_chat_id': ADMIN_CHAT_ID,
                'user_id': user_id,
                'error': str(e)
            }
        )
        # Fallback: try sending to ADMIN_USER_ID instead
        try:
            ADMIN_USER_ID = int(secrets.get("ADMIN_USER_ID", 0))
            if ADMIN_USER_ID:
                await telegram_api_call_with_retry(
                    bot_client.send_message,
                    ADMIN_USER_ID,
                    f"🔔 Lead logged: {full_name} (@{username})",
                    buttons=buttons
                )
                logger.info(
                    f"✅ Admin buttons sent successfully to ADMIN_USER_ID {ADMIN_USER_ID} as fallback",
                    extra={
                        'action': 'admin_buttons_fallback_success',
                        'admin_user_id': ADMIN_USER_ID,
                        'user_id': user_id
                    }
                )
        except Exception as e2:
            logger.error(
                f"Fallback also failed: {e2}",
                extra={
                    'action': 'admin_buttons_fallback_failed',
                    'user_id': user_id,
                    'error': str(e2)
                }
            )

@client.on(events.NewMessage(outgoing=True))
async def handle_outgoing_message(event):
    if event.is_group or event.is_channel:
        return

    message_text = event.raw_text.strip()
    logger.info(
        f"📤 Outgoing message detected: '{message_text}'",
        extra={
            'action': 'outgoing_message_detected',
            'message_length': len(message_text)
        }
    )

    trigger_words = ["hello", "from", "hacken"]
    message_lower = message_text.lower()
    logger.info(
        f"🔍 Checking for trigger words {trigger_words} in: '{message_lower}'",
        extra={
            'action': 'trigger_check',
            'trigger_words': trigger_words,
            'message_text': message_lower
        }
    )

    if all(word in message_lower for word in trigger_words):
        logger.info(
            "🎯 TRIGGER DETECTED! Starting lead flow...",
            extra={'action': 'trigger_detected'}
        )
        chat = await event.get_chat()
        user_id = chat.id

        if user_id not in initiated_users:
            initiated_users.append(user_id)
            save_storage()
            logger.info(
                f"Initiated contact with user {user_id}",
                extra={
                    'action': 'user_initiated',
                    'user_id': user_id
                }
            )

        if user_id in handled_users:
            logger.info(
                f"User {user_id} already handled - continuing with duplicate processing",
                extra={
                    'action': 'user_duplicate_processing',
                    'user_id': user_id
                }
            )

        try:
            entity = await telegram_api_call_with_retry(client.get_entity, user_id)
            full_name = f"{entity.first_name or ''} {entity.last_name or ''}".strip()
            username = entity.username or ""

            try:
                full = await telegram_api_call_with_retry(client, GetFullUserRequest(user_id))
                bio = full.full_user.about or ""
            except Exception as e:
                bio = ""
                logger.warning(
                    f"Failed to fetch user bio: {e}",
                    extra={
                        'action': 'bio_fetch_failed',
                        'user_id': user_id,
                        'error': str(e)
                    }
                )

            messages = await telegram_api_call_with_retry(client.get_messages, user_id, limit=1)
            last_message = messages[0].message if messages else ""

            timestamp = datetime.now(timezone.utc).isoformat()
            tags = infer_tags(last_message + " " + bio)

            # Debug logging for company extraction
            bio_text = last_message + " " + bio
            logger.info(
                f"Company extraction debug - Bio: '{bio}', Last message: '{last_message}', Combined: '{bio_text}'",
                extra={
                    'action': 'company_extraction_debug',
                    'user_id': user_id,
                    'bio': bio,
                    'last_message': last_message,
                    'combined_text': bio_text
                }
            )

            company = infer_company(bio_text, entity)

            logger.info(
                f"Processing lead for {full_name} ({username}) — Tags: {tags}, Company: {company}",
                extra={
                    'action': 'lead_processing',
                    'user_id': user_id,
                    'username': username,
                    'full_name': full_name,
                    'company': company,
                    'tags': tags
                }
            )

            log_lead(user_id, full_name, username, timestamp, bio=bio, message=last_message, company=company, tags=tags)
            handled_users.append(user_id)
            save_storage()

            # Log user action for audit trail
            log_user_action(
                'lead_captured',
                user_id,
                username,
                full_name=full_name,
                company=company,
                tags=tags,
                trigger_message=message_text[:100]  # First 100 chars
            )

            await asyncio.sleep(3)

            await telegram_api_call_with_retry(
                event.respond,
                "Hacken\n"
                "From pre-launch audits to post-deployment monitoring, we help Web3 teams build securely from day one.\n"
                "Your roadmap is unique. So is our approach.\n"
                "https://hacken.io"
            )

            await send_admin_buttons(user_id, username, full_name)

        except Exception as e:
            logger.error(
                f"Error processing lead flow for {user_id}: {e}",
                extra={
                    'action': 'lead_processing_error',
                    'user_id': user_id,
                    'error': str(e)
                }
            )

@client.on(events.NewMessage(incoming=True))
async def handle_incoming_message(event):
    if event.is_group or event.is_channel:
        return

    sender = await event.get_sender()
    if sender.bot:
        return

    message_text = event.raw_text.strip()
    user_id = sender.id

    logger.info(
        f"New message from {user_id}: {message_text}",
        extra={
            'action': 'incoming_message',
            'user_id': user_id,
            'message_length': len(message_text)
        }
    )

    if user_id in initiated_users:
        logger.info(
            f"Additional message from initiated user {user_id}",
            extra={
                'action': 'initiated_user_message',
                'user_id': user_id
            }
        )
    else:
        logger.info(
            f"Message from non-initiated user {user_id} - ignoring",
            extra={
                'action': 'non_initiated_user_message',
                'user_id': user_id
            }
        )

@bot_client.on(events.CallbackQuery)
async def handle_callback(event):
    data = event.data.decode("utf-8")
    if ":" not in data:
        return

    action, user_id = data.split(":")
    logger.info(
        f"Callback received: {action} for user {user_id}",
        extra={
            'action': 'callback_received',
            'callback_action': action,
            'user_id': user_id,
            'sender_id': event.sender_id
        }
    )

    # Check if sender is authorized (either ADMIN_CHAT_ID or AUTHORIZED_USER_IDS from secrets)
    ADMIN_USER_ID = int(secrets.get("ADMIN_USER_ID", 0))
    authorized_users_str = secrets.get("AUTHORIZED_USER_IDS", str(ADMIN_USER_ID))
    AUTHORIZED_USER_IDS = [int(uid.strip()) for uid in authorized_users_str.split(",") if uid.strip()]

    # Debug logging
    logger.info(
        f"Authorization check: sender={event.sender_id}, admin_chat={ADMIN_CHAT_ID}, authorized_users={AUTHORIZED_USER_IDS}",
        extra={
            'action': 'authorization_debug',
            'sender_id': event.sender_id,
            'admin_chat_id': ADMIN_CHAT_ID,
            'authorized_user_ids': AUTHORIZED_USER_IDS
        }
    )

    if event.sender_id != ADMIN_CHAT_ID and event.sender_id not in AUTHORIZED_USER_IDS:
        await event.answer("Access denied", alert=True)

        # Log security event for unauthorized access
        log_security_event(
            'unauthorized_callback_attempt',
            severity='warning',
            sender_id=event.sender_id,
            callback_action=action,
            target_user_id=user_id
        )
        return

    if action == "tag_audit":
        append_tag_to_lead(user_id, "Audit")
        await event.answer("Tagged as Audit")
        log_admin_action('tag_added', event.sender_id, user_id, tag='Audit')
    elif action == "tag_pen_test":
        append_tag_to_lead(user_id, "Pen Test")
        await event.answer("Tagged as Pen Test")
        log_admin_action('tag_added', event.sender_id, user_id, tag='Pen Test')
    elif action == "tag_bug":
        append_tag_to_lead(user_id, "Bug Bounty")
        await event.answer("Tagged as Bug Bounty")
        log_admin_action('tag_added', event.sender_id, user_id, tag='Bug Bounty')
    elif action == "tag_monitoring":
        append_tag_to_lead(user_id, "Monitoring")
        await event.answer("Tagged as Monitoring")
        log_admin_action('tag_added', event.sender_id, user_id, tag='Monitoring')
    elif action == "status_warm":
        update_lead_status(user_id, "Warm Lead")
        await event.answer("Status updated to Warm")
        log_admin_action('status_updated', event.sender_id, user_id, new_status='Warm Lead')
    elif action == "export":
        await event.answer("Exporting... (not implemented)", alert=True)
        log_admin_action('export_requested', event.sender_id, user_id)
    elif action == "create_group":
        from group_manager import create_lead_group
        full_name = "Unknown"
        username = ""
        try:
            entity = await telegram_api_call_with_retry(client.get_entity, int(user_id))
            full_name = f"{entity.first_name or ''} {entity.last_name or ''}".strip()
            username = entity.username or ""
        except Exception as e:
            logger.warning(
                f"Failed to resolve user entity for group creation: {e}",
                extra={
                    'action': 'entity_resolution_failed',
                    'user_id': user_id,
                    'error': str(e)
                }
            )
        await create_lead_group(client, full_name, username, int(user_id))
        await event.answer("Group created")

async def main():
    """Start both user client and bot client with enhanced error handling"""
    logger.info("Lead tracker starting...", extra={'action': 'startup'})

    try:
        # Start user client for monitoring messages
        await client.start()
        logger.info("✅ User client started", extra={'action': 'user_client_started'})

        # Start bot client for sending admin buttons
        await bot_client.start(bot_token=CONTROL_BOT_TOKEN)
        logger.info("✅ Bot client started", extra={'action': 'bot_client_started'})

        logger.info("🚀 Both clients running - ready to process leads!", extra={'action': 'ready'})

        # Run until disconnected
        await client.run_until_disconnected()

    except Exception as e:
        logger.error(
            f"Fatal error in main: {e}",
            extra={'action': 'fatal_error', 'error': str(e)},
            exc_info=True
        )
        raise
    finally:
        # Clean up session files if they were decrypted
        for session_file in [f"{SESSION_NAME}.session", f"{BOT_SESSION_NAME}.session"]:
            if os.path.exists(session_file) and os.path.exists(f"{session_file}.enc"):
                try:
                    os.remove(session_file)
                    logger.info(f"Cleaned up decrypted session: {session_file}")
                except Exception as e:
                    logger.warning(f"Failed to clean up session {session_file}: {e}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
