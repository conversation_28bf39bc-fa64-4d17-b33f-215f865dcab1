#!/usr/bin/env python3
"""
Centralized logging configuration for Telegram Bot
Provides structured logging with audit trails and log rotation
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
import json

class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging with audit trail support"""
    
    def format(self, record):
        # Base log entry
        log_entry = {
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Add extra fields if present
        if hasattr(record, 'action'):
            log_entry['action'] = record.action
        
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
            
        if hasattr(record, 'username'):
            log_entry['username'] = record.username
            
        if hasattr(record, 'group_id'):
            log_entry['group_id'] = record.group_id
            
        if hasattr(record, 'group_name'):
            log_entry['group_name'] = record.group_name
            
        if hasattr(record, 'error'):
            log_entry['error'] = record.error
            
        if hasattr(record, 'wait_time'):
            log_entry['wait_time'] = record.wait_time
            
        if hasattr(record, 'attempt'):
            log_entry['attempt'] = record.attempt
            
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add process info
        log_entry['process_id'] = os.getpid()
        
        return json.dumps(log_entry, ensure_ascii=False)

class HumanReadableFormatter(logging.Formatter):
    """Human-readable formatter for console output"""
    
    def __init__(self):
        super().__init__(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    def format(self, record):
        # Add extra context if available
        extra_info = []
        
        if hasattr(record, 'action'):
            extra_info.append(f"action={record.action}")
            
        if hasattr(record, 'user_id'):
            extra_info.append(f"user_id={record.user_id}")
            
        if hasattr(record, 'username'):
            extra_info.append(f"username={record.username}")
        
        if extra_info:
            record.msg = f"{record.msg} [{', '.join(extra_info)}]"
        
        return super().format(record)

def setup_logging(
    log_level=logging.INFO,
    log_dir="/var/log/telegram-bot",
    app_name="telegram-bot",
    max_bytes=10*1024*1024,  # 10MB
    backup_count=5,
    console_output=True
):
    """
    Setup centralized logging configuration
    
    Args:
        log_level: Logging level (default: INFO)
        log_dir: Directory for log files
        app_name: Application name for log files
        max_bytes: Maximum size per log file
        backup_count: Number of backup files to keep
        console_output: Whether to output to console
    """
    
    # Create log directory
    log_path = Path(log_dir)
    log_path.mkdir(parents=True, exist_ok=True)
    
    # Set permissions on log directory
    try:
        os.chmod(log_path, 0o755)
    except PermissionError:
        pass  # May not have permission in development
    
    # Root logger configuration
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # File handler for structured logs (JSON format)
    structured_log_file = log_path / f"{app_name}.log"
    file_handler = logging.handlers.RotatingFileHandler(
        structured_log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(file_handler)
    
    # File handler for audit trail (separate file for important events)
    audit_log_file = log_path / f"{app_name}-audit.log"
    audit_handler = logging.handlers.RotatingFileHandler(
        audit_log_file,
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )
    audit_handler.setLevel(logging.WARNING)  # Only warnings and errors
    audit_handler.setFormatter(StructuredFormatter())
    root_logger.addHandler(audit_handler)
    
    # Console handler for development/debugging
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(HumanReadableFormatter())
        root_logger.addHandler(console_handler)
    
    # Set file permissions
    try:
        os.chmod(structured_log_file, 0o644)
        os.chmod(audit_log_file, 0o644)
    except (PermissionError, FileNotFoundError):
        pass  # May not exist yet or no permission
    
    # Configure specific loggers
    configure_specific_loggers()
    
    # Log startup message
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging system initialized",
        extra={
            'action': 'logging_initialized',
            'log_dir': str(log_dir),
            'log_level': logging.getLevelName(log_level),
            'structured_log': str(structured_log_file),
            'audit_log': str(audit_log_file)
        }
    )

def configure_specific_loggers():
    """Configure specific loggers for different components"""
    
    # Reduce noise from external libraries
    logging.getLogger('telethon').setLevel(logging.WARNING)
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('oauth2client').setLevel(logging.WARNING)
    logging.getLogger('googleapiclient').setLevel(logging.WARNING)
    
    # Set appropriate levels for our modules
    logging.getLogger('lead_tracker').setLevel(logging.INFO)
    logging.getLogger('group_manager').setLevel(logging.INFO)
    logging.getLogger('google_sheets').setLevel(logging.INFO)
    logging.getLogger('secrets_loader').setLevel(logging.INFO)

def get_audit_logger():
    """Get a logger specifically for audit events"""
    audit_logger = logging.getLogger('audit')
    audit_logger.setLevel(logging.INFO)
    return audit_logger

def log_user_action(action, user_id, username=None, **kwargs):
    """
    Log user actions for audit trail
    
    Args:
        action: Action performed
        user_id: User ID
        username: Username (optional)
        **kwargs: Additional context
    """
    audit_logger = get_audit_logger()
    
    extra = {
        'action': action,
        'user_id': user_id,
        'username': username or 'unknown'
    }
    extra.update(kwargs)
    
    audit_logger.info(f"User action: {action}", extra=extra)

def log_admin_action(action, admin_id, target_user_id=None, **kwargs):
    """
    Log admin actions for audit trail
    
    Args:
        action: Action performed
        admin_id: Admin user ID
        target_user_id: Target user ID (if applicable)
        **kwargs: Additional context
    """
    audit_logger = get_audit_logger()
    
    extra = {
        'action': action,
        'admin_id': admin_id,
        'target_user_id': target_user_id
    }
    extra.update(kwargs)
    
    audit_logger.warning(f"Admin action: {action}", extra=extra)

def log_system_event(event, **kwargs):
    """
    Log system events for audit trail
    
    Args:
        event: System event
        **kwargs: Additional context
    """
    audit_logger = get_audit_logger()
    
    extra = {
        'action': 'system_event',
        'event': event
    }
    extra.update(kwargs)
    
    audit_logger.info(f"System event: {event}", extra=extra)

def log_security_event(event, severity='warning', **kwargs):
    """
    Log security events for audit trail
    
    Args:
        event: Security event
        severity: Event severity (info, warning, error)
        **kwargs: Additional context
    """
    audit_logger = get_audit_logger()
    
    extra = {
        'action': 'security_event',
        'event': event,
        'severity': severity
    }
    extra.update(kwargs)
    
    if severity == 'error':
        audit_logger.error(f"Security event: {event}", extra=extra)
    elif severity == 'warning':
        audit_logger.warning(f"Security event: {event}", extra=extra)
    else:
        audit_logger.info(f"Security event: {event}", extra=extra)

# Convenience function for easy import
def init_logging(console=True, debug=False):
    """
    Initialize logging with sensible defaults
    
    Args:
        console: Enable console output
        debug: Enable debug level logging
    """
    level = logging.DEBUG if debug else logging.INFO
    
    # Try production path first, fall back to local
    try:
        setup_logging(
            log_level=level,
            log_dir="/var/log/telegram-bot",
            console_output=console
        )
    except PermissionError:
        # Fallback for development
        setup_logging(
            log_level=level,
            log_dir="./logs",
            console_output=console
        )

if __name__ == "__main__":
    # Test the logging configuration
    init_logging(console=True, debug=True)
    
    logger = logging.getLogger(__name__)
    
    # Test different log levels and structured data
    logger.debug("Debug message")
    logger.info("Info message", extra={'action': 'test', 'user_id': 12345})
    logger.warning("Warning message", extra={'action': 'test_warning'})
    logger.error("Error message", extra={'action': 'test_error', 'error': 'Test error'})
    
    # Test audit functions
    log_user_action('test_action', 12345, 'testuser', extra_field='test')
    log_admin_action('test_admin_action', 67890, 12345)
    log_system_event('test_system_event', component='test')
    log_security_event('test_security_event', severity='warning', ip='127.0.0.1')
    
    print("Logging test completed. Check logs directory for output.")
