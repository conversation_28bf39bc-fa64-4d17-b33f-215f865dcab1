# Telegram Bot Log Rotation Configuration
# Install with: sudo cp logrotate.conf /etc/logrotate.d/telegram-bot

# Main application logs
/var/log/telegram-bot/*.log {
    # Rotate daily
    daily
    
    # Keep 30 days of logs
    rotate 30
    
    # Compress old logs (saves disk space)
    compress
    
    # Don't compress the most recent rotated log
    delaycompress
    
    # Don't rotate if log is empty
    notifempty
    
    # Don't error if log file is missing
    missingok
    
    # Create new log file with specified permissions
    create 644 ubuntu ubuntu
    
    # Use date as suffix for rotated files
    dateext
    
    # Use YYYY-MM-DD format for date suffix
    dateformat -%Y-%m-%d
    
    # Copy and truncate original file instead of moving it
    # This ensures the application can continue writing to the same file
    copytruncate
    
    # Run commands after rotation
    postrotate
        # Send SIGUSR1 to the bot process to reload logs (if supported)
        # or restart the service if needed
        if systemctl is-active telegram-bot >/dev/null 2>&1; then
            systemctl reload telegram-bot >/dev/null 2>&1 || true
        fi
    endscript
}

# Audit logs (keep longer)
/var/log/telegram-bot/*-audit.log {
    # Rotate weekly for audit logs
    weekly
    
    # Keep 12 weeks (3 months) of audit logs
    rotate 12
    
    # Compress old logs
    compress
    delaycompress
    
    # Don't rotate if log is empty
    notifempty
    
    # Don't error if log file is missing
    missingok
    
    # Create new log file with specified permissions
    create 644 ubuntu ubuntu
    
    # Use date as suffix
    dateext
    dateformat -%Y-%m-%d
    
    # Copy and truncate
    copytruncate
    
    postrotate
        if systemctl is-active telegram-bot >/dev/null 2>&1; then
            systemctl reload telegram-bot >/dev/null 2>&1 || true
        fi
    endscript
}

# Backup logs (keep shorter)
/var/log/telegram-bot/backup*.log {
    # Rotate monthly for backup logs
    monthly
    
    # Keep 6 months of backup logs
    rotate 6
    
    # Compress old logs
    compress
    delaycompress
    
    # Don't rotate if log is empty
    notifempty
    
    # Don't error if log file is missing
    missingok
    
    # Create new log file with specified permissions
    create 644 ubuntu ubuntu
    
    # Use date as suffix
    dateext
    dateformat -%Y-%m-%d
    
    # Copy and truncate
    copytruncate
}

# System logs (if any)
/var/log/telegram-bot/system*.log {
    # Rotate weekly
    weekly
    
    # Keep 8 weeks
    rotate 8
    
    # Compress old logs
    compress
    delaycompress
    
    # Don't rotate if log is empty
    notifempty
    
    # Don't error if log file is missing
    missingok
    
    # Create new log file with specified permissions
    create 644 ubuntu ubuntu
    
    # Use date as suffix
    dateext
    dateformat -%Y-%m-%d
    
    # Copy and truncate
    copytruncate
}
