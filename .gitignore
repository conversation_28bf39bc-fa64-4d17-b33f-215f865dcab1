# Telegram Session Files
*.session
*.session-journal

# Environment Files
.env
.env.*
!.env.template

# Google Credentials
credentials.json

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# Logs
*.log
logs/

# Backups
backups/
*.tar.gz
*.zip

# Storage
storage.json

# AWS
.aws/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db
